import { Header } from "@/components/header";
import { AssessmentCard } from "@/components/assessment-card";
import { FeatureCard } from "@/components/feature-card";
import { EducationCard } from "@/components/education-card";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  Calendar, TrendingUp, Users, BookOpen, Play, Brain,
  Shield, Heart, Zap, Target, Star, ArrowRight
} from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const featuredAssessments = [
    {
      id: "gse",
      title: "General Self-Efficacy Scale",
      description: "Quick assessment untuk mengukur keyakinan diri Anda dalam menghada<PERSON> tantangan",
      duration: "3-5 menit",
      questions: 10,
      category: "confidence" as const,
      completionRate: 75,
      lastCompleted: "2 hari lalu",
      difficulty: "easy" as const,
    },
    {
      id: "dass42",
      title: "DASS-42",
      description: "Assessment komprehensif untuk depresi, kecemasan, dan stres dengan analisis mendalam",
      duration: "10-15 menit",
      questions: 42,
      category: "stress" as const,
      completionRate: 0,
      difficulty: "medium" as const,
    },
  ];

  const features = [
    {
      icon: Brain,
      title: "AI-Powered Analysis",
      description: "Analisis cerdas dengan teknologi AI untuk hasil yang akurat",
      badge: "Terbaru",
      gradient: "bg-gradient-to-br from-purple-500 to-indigo-500"
    },
    {
      icon: Shield,
      title: "Data Aman",
      description: "Privasi terjamin dengan enkripsi tingkat enterprise",
      gradient: "bg-gradient-to-br from-green-500 to-emerald-500"
    },
    {
      icon: Target,
      title: "Personalisasi",
      description: "Rekomendasi yang disesuaikan dengan profil Anda",
      gradient: "bg-gradient-to-br from-orange-500 to-red-500"
    }
  ];

  const educationContent = [
    {
      title: "Mengelola Stres dalam Kehidupan Santri",
      description: "Teknik praktis untuk mengatasi tekanan akademik dan spiritual",
      duration: "5 menit",
      views: 1420,
      category: "Stres Management",
      isNew: true
    },
    {
      title: "Membangun Kepercayaan Diri",
      description: "Strategi meningkatkan self-efficacy dalam lingkungan pesantren",
      duration: "8 menit", 
      views: 892,
      category: "Self Development"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-soft pb-24">
      <Header
        title="TOKENPEDIA"
        subtitle={isAuthenticated
          ? `Selamat datang, ${user?.user_metadata?.full_name || user?.email || 'User'}!`
          : "Selamat datang! Silakan login untuk pengalaman yang lebih personal."
        }
        showNotification={isAuthenticated}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-8">
        {/* Hero Section with Logo */}
        <section className="relative overflow-hidden bg-gradient-hero text-white animate-fade-in">
          <div className="absolute inset-0 bg-gradient-primary opacity-90"></div>
          <div className="relative px-6 py-12">
            <div className="text-center max-w-sm mx-auto">
              <div className="mb-6">
                {/* Logo */}
                <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm mb-6 p-2">
                  <img 
                    src="/lovable-uploads/4272cd26-9bf7-47ab-8e0a-7459214f56ed.png" 
                    alt="Token Pedia Logo" 
                    className="w-full h-full object-contain rounded-full"
                    onError={(e) => {
                      // Fallback to Heart icon if logo fails to load
                      e.currentTarget.style.display = 'none';
                      const fallback = e.currentTarget.nextElementSibling;
                      if (fallback) fallback.classList.remove('hidden');
                    }}
                  />
                  <Heart className="h-10 w-10 text-white hidden" />
                </div>
                <h1 className="font-display font-bold text-2xl leading-tight mb-3">
                  Kesehatan Mental <br />
                  <span className="text-accent">Santri Digital</span>
                </h1>
                <p className="text-white/90 text-sm leading-relaxed">
                  Platform komprehensif untuk mendukung kesehatan mental santri dengan assessment modern dan edukasi terintegrasi berbasis digitalisasi pesantren
                </p>
              </div>
              
              <div className="space-y-3">
                <Button 
                  onClick={() => navigate('/assessment')}
                  size="lg" 
                  className="w-full bg-white text-primary hover:bg-white/90 font-semibold shadow-glow hover:shadow-white/20 transition-all duration-300 group"
                >
                  <Brain className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                  Mulai Assessment
                </Button>
                
                <Button 
                  onClick={() => navigate('/education')}
                  variant="outline"
                  size="lg" 
                  className="w-full border-white/30 text-primary hover:bg-white/10 font-semibold backdrop-blur-sm"
                >
                  <BookOpen className="mr-2 h-5 w-5" />
                  Jelajahi Edukasi
                </Button>
              </div>
            </div>
          </div>
          
          {/* Enhanced Floating Elements */}
          <div className="absolute top-4 left-4 w-3 h-3 bg-white/30 rounded-full animate-pulse"></div>
          <div className="absolute bottom-12 right-6 w-2 h-2 bg-accent/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/3 right-4 w-1 h-1 bg-white/40 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-1/4 left-8 w-2 h-2 bg-white/20 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        </section>

        {/* Enhanced Stats */}
        <div className="grid grid-cols-3 gap-3">
          {[
            { icon: Calendar, value: "7", label: "Hari ini", color: "text-primary" },
            { icon: Users, value: "156", label: "Pengguna", color: "text-success" },
            { icon: BookOpen, value: "23", label: "Artikel", color: "text-accent" }
          ].map((stat, index) => (
            <Card key={index} className="p-4 text-center shadow-soft border-0 bg-gradient-card card-interactive animate-slide-up" 
                  style={{ animationDelay: `${index * 100}ms` }}>
              <stat.icon className={`h-6 w-6 mx-auto mb-2 ${stat.color}`} />
              <div className="text-2xl font-bold text-foreground">{stat.value}</div>
              <div className="text-2xs text-muted-foreground font-medium">{stat.label}</div>
            </Card>
          ))}
        </div>

        {/* Featured Assessments */}
        <section className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-display font-bold text-xl text-foreground">Assessment Populer</h3>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/assessment')}
              className="text-primary hover:text-primary/80 font-semibold"
            >
              Lihat Semua
              <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>

          {featuredAssessments.map((assessment, index) => (
            <AssessmentCard
              key={assessment.id}
              title={assessment.title}
              description={assessment.description}
              duration={assessment.duration}
              questions={assessment.questions}
              category={assessment.category}
              completionRate={assessment.completionRate}
              lastCompleted={assessment.lastCompleted}
              difficulty={assessment.difficulty}
              onStart={() => navigate(`/assessment/${assessment.id}`)}
              style={{ animationDelay: `${(index + 3) * 150}ms` }}
            />
          ))}
        </section>

        {/* Features Section */}
        <section className="space-y-4">
          <h3 className="font-display font-bold text-xl text-foreground">Fitur Unggulan</h3>
          <div className="grid gap-4">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                badge={feature.badge}
                gradient={feature.gradient}
                delay={index * 100}
              />
            ))}
          </div>
        </section>

        {/* Education Section */}
        <section className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-display font-bold text-xl text-foreground">Edukasi Terbaru</h3>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => navigate('/education')}
              className="text-primary hover:text-primary/80 font-semibold"
            >
              Lihat Semua
            </Button>
          </div>
          
          <div className="space-y-4">
            {educationContent.map((content, index) => (
              <EducationCard
                key={index}
                title={content.title}
                description={content.description}
                duration={content.duration}
                views={content.views}
                category={content.category}
                isNew={content.isNew}
                delay={index * 100}
                onPlay={() => navigate('/videos')}
              />
            ))}
          </div>
        </section>
      </div>
      
      <BottomNav />
    </div>
  );
};

export default Index;
