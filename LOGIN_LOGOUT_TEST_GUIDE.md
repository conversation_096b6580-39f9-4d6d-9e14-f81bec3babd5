# Login/Logout Test Guide

## 🚀 Quick Start Testing

Your application is running at: **http://127.0.0.1:8081/**

## 📋 Test Scenarios

### 1. **Anonymous User Experience**

**Steps to test:**
1. Open http://127.0.0.1:8081/
2. Notice the welcome message shows "Selamat datang! <PERSON>lakan login untuk pengalaman yang lebih personal."
3. Check the header - you should see a login icon (arrow pointing right)
4. Go to `/profile` page - you should see a login prompt
5. Try taking an assessment - it should work without login (anonymous mode)

**Expected Results:**
- ✅ App works without login
- ✅ Generic welcome message shown
- ✅ Login prompt in profile page
- ✅ Assessments work in anonymous mode
- ✅ Login icon visible in header

---

### 2. **User Registration**

**Steps to test:**
1. Click the login icon in header OR go to http://127.0.0.1:8081/login
2. Click "Daftar sekarang" link
3. Fill out the registration form:
   - **<PERSON><PERSON>**: Test User
   - **Email**: <EMAIL> (use a real email if you want to test email verification)
   - **Password**: password123
   - **Konfirmasi Password**: password123
4. Click "Daftar"

**Expected Results:**
- ✅ Registration form validates input
- ✅ Success message appears: "Registrasi Berhasil"
- ✅ Redirected to login page
- ✅ Email verification sent (if using real email)

---

### 3. **User Login**

**Steps to test:**
1. Go to http://127.0.0.1:8081/login
2. Enter credentials:
   - **Email**: <EMAIL>
   - **Password**: password123
3. Click "Masuk"

**Expected Results:**
- ✅ Success message: "Login Berhasil"
- ✅ Redirected to home page
- ✅ Welcome message now shows: "Selamat datang, Test User!"
- ✅ Header shows user icon instead of login icon
- ✅ Notifications icon appears in header

---

### 4. **Authenticated User Experience**

**After successful login, test these features:**

#### **Header User Menu:**
1. Click the user icon in header
2. Check dropdown menu shows:
   - User name and email
   - "Profil" option
   - "Riwayat Assessment" option
   - "Logout" option (in red)

#### **Profile Page:**
1. Go to `/profile` page
2. Should show:
   - User avatar with initials
   - Full name and email
   - Join date
   - Stats cards (Assessment, Selesai, Artikel Dibaca)
   - Menu options (Riwayat, Pengaturan, Bantuan, Keluar)

#### **Assessment Integration:**
1. Start any assessment (e.g., GSE)
2. Assessment should now use your authenticated user ID
3. Progress should be saved to your account

---

### 5. **Logout Testing**

**Method 1 - Header Menu:**
1. Click user icon in header
2. Click "Logout" (red option)
3. Confirm logout

**Method 2 - Profile Page:**
1. Go to profile page
2. Click "Keluar" menu item
3. Confirm logout

**Expected Results:**
- ✅ Success message: "Logout Berhasil"
- ✅ Redirected to home page
- ✅ Welcome message returns to generic version
- ✅ Header shows login icon again
- ✅ Profile page shows login prompt again
- ✅ Anonymous user ID cleared from localStorage

---

### 6. **Password Reset Testing**

**Steps to test:**
1. Go to login page
2. Click "Lupa password?" link
3. Enter your email address
4. Click submit

**Expected Results:**
- ✅ Success message about email sent
- ✅ Reset email received (if using real email)
- ✅ Reset link works and redirects to change password page

---

### 7. **Session Persistence Testing**

**Steps to test:**
1. Login to your account
2. Refresh the page
3. Close and reopen the browser tab
4. Navigate to different pages

**Expected Results:**
- ✅ User remains logged in after refresh
- ✅ User remains logged in after reopening tab
- ✅ Authentication state persists across navigation

---

## 🔧 Advanced Testing

### **Assessment Data Isolation**
1. Create two different user accounts
2. Take assessments with each account
3. Verify each user only sees their own data

### **Anonymous to Authenticated Migration**
1. Start an assessment as anonymous user
2. Login during the assessment
3. Verify the session continues properly

### **Error Handling**
1. Try logging in with wrong credentials
2. Try registering with existing email
3. Try accessing protected routes
4. Verify appropriate error messages

---

## 🐛 Troubleshooting

### **If Login Doesn't Work:**
- Check browser console for errors
- Verify Supabase credentials in `.env` file
- Check if email verification is required

### **If Logout Doesn't Work:**
- Check browser console for errors
- Verify the logout function is called
- Check if localStorage is cleared

### **If Assessment Data Isn't Saved:**
- Check browser network tab for API calls
- Verify RLS policies in Supabase
- Check user authentication status

---

## 📱 Mobile Testing

Test the same scenarios on mobile devices:
- Touch interactions work properly
- Responsive design adapts correctly
- Authentication flows work on mobile browsers

---

## ✅ Test Checklist

- [ ] Anonymous user can use the app
- [ ] Registration form works
- [ ] Login form works
- [ ] User menu appears after login
- [ ] Profile page shows user info
- [ ] Assessments work for authenticated users
- [ ] Logout works from header menu
- [ ] Logout works from profile page
- [ ] Session persists across page refreshes
- [ ] Password reset flow works
- [ ] Error messages display correctly
- [ ] Mobile experience works properly

---

**🎯 All tests passing means your authentication system is working perfectly!**
