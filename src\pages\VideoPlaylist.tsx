import { useState } from "react";
import { Header } from "@/components/header";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Play, Clock, Eye, ExternalLink } from "lucide-react";
import { VideoModal } from "@/components/video-modal";

const videos = [
  {
    id: "1",
    title: "Seri 3 Manajemen Stres Dalam Mencegah Pernikahan Muda Melalui Budaya Lokal",
    url: "https://youtu.be/_22feazq014",
    thumbnail: `https://img.youtube.com/vi/_22feazq014/maxresdefault.jpg`,
    duration: "15:32",
    views: "1.2K",
    category: "Manajemen Stres"
  },
  {
    id: "2", 
    title: "Seri 1 Kesakralan dan <PERSON>losof<PERSON>",
    url: "https://youtu.be/rGYAoVZxau4",
    thumbnail: `https://img.youtube.com/vi/rGYAoVZxau4/maxresdefault.jpg`,
    duration: "18:45",
    views: "856",
    category: "Filosofi"
  },
  {
    id: "3",
    title: "Eps 3 Dokter Medis",
    url: "https://youtu.be/GCN6d7A-yyk", 
    thumbnail: `https://img.youtube.com/vi/GCN6d7A-yyk/maxresdefault.jpg`,
    duration: "12:20",
    views: "2.1K",
    category: "Medis"
  },
  {
    id: "4",
    title: "Seri 4 Manajemen Beban Dalam Pencegahan Pernikahan Muda Melalui Budaya Lokal",
    url: "https://youtu.be/NCIZJWA-_Cw",
    thumbnail: `https://img.youtube.com/vi/NCIZJWA-_Cw/maxresdefault.jpg`,
    duration: "20:15",
    views: "934",
    category: "Manajemen Beban"
  },
  {
    id: "5",
    title: "Eps 2 Orang Tua Episode 2",
    url: "https://youtu.be/btSo9R8vGH0",
    thumbnail: `https://img.youtube.com/vi/btSo9R8vGH0/maxresdefault.jpg`,
    duration: "14:28",
    views: "1.8K",
    category: "Parenting"
  },
  {
    id: "6",
    title: "Cara Santri Cari Bantuan Kesehatan Jiwa",
    url: "https://youtu.be/X9tNDlCdttw",
    thumbnail: `https://img.youtube.com/vi/X9tNDlCdttw/maxresdefault.jpg`,
    duration: "16:42",
    views: "3.2K",
    category: "Kesehatan Jiwa"
  },
  {
    id: "7",
    title: "Seri 2 Perawatan Dan Pencegahan Pernikahan Muda",
    url: "https://www.youtube.com/watch?v=LJ3aJNA9ABs",
    thumbnail: `https://img.youtube.com/vi/LJ3aJNA9ABs/maxresdefault.jpg`,
    duration: "22:10",
    views: "1.5K",
    category: "Pencegahan"
  }
];

export default function VideoPlaylist() {
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleVideoClick = (video: any) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-gradient-soft pb-20">
      <Header 
        title="Video Edukasi" 
        subtitle="Kumpulan video kesehatan mental untuk santri"
      />
      
      <div className="p-4 max-w-md mx-auto space-y-4">
        <div className="text-center py-4">
          <h2 className="font-display font-semibold text-lg text-foreground mb-2">
            Playlist Video Edukasi
          </h2>
          <p className="text-muted-foreground text-sm leading-relaxed">
            Video-video edukatif tentang kesehatan mental, manajemen stres, dan kehidupan santri
          </p>
        </div>

        {videos.map((video, index) => (
          <Card 
            key={video.id}
            className="overflow-hidden shadow-soft border-0 bg-gradient-card hover:shadow-medium transition-all duration-200 cursor-pointer animate-slide-up"
            style={{ animationDelay: `${index * 100}ms` }}
            onClick={() => handleVideoClick(video)}
          >
            <div className="relative">
              <img 
                src={video.thumbnail}
                alt={video.title}
                className="w-full h-32 object-cover"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center group-hover:bg-black/30 transition-colors">
                <div className="p-3 rounded-full bg-white/90 shadow-medium">
                  <Play className="h-6 w-6 text-primary fill-current" />
                </div>
              </div>
              <div className="absolute bottom-2 right-2 px-2 py-1 bg-black/80 rounded text-white text-xs flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {video.duration}
              </div>
            </div>
            
            <div className="p-4">
              <div className="flex items-start gap-3">
                <div className="flex-1">
                  <h3 className="font-medium text-foreground text-sm leading-relaxed mb-2 line-clamp-2">
                    {video.title}
                  </h3>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      {video.category}
                    </Badge>
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 text-muted-foreground text-xs">
                        <Eye className="h-3 w-3" />
                        {video.views}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(video.url, '_blank');
                        }}
                        className="h-6 px-2 text-xs text-primary hover:text-primary/80"
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        YouTube
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      <BottomNav />
      
      {/* Video Modal */}
      <VideoModal 
        video={selectedVideo}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedVideo(null);
        }}
      />
    </div>
  );
}