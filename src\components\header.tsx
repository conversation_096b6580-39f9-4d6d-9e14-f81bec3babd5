import { cn } from "@/lib/utils";
import { Bell, Search, Menu as MenuI<PERSON>, Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";

interface HeaderProps {
  title: string;
  subtitle?: string;
  showNotification?: boolean;
  showMenu?: boolean;
  className?: string;
}

export function Header({ 
  title, 
  subtitle, 
  showNotification = true, 
  showMenu = false,
  className 
}: HeaderProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [notificationCount] = useState(3);

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <header className={cn(
      "sticky top-0 z-40 w-full border-b border-border-accent/50",
      "glass backdrop-blur-xl",
      className
    )}>
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5 pointer-events-none" />
      
      <div className="relative max-w-md mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo/Title Section */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              {/* App Icon */}
              <div className="p-2 rounded-xl bg-gradient-primary shadow-soft">
                <div className="w-6 h-6 bg-white/90 rounded-lg flex items-center justify-center">
                  <div className="w-3 h-3 bg-primary rounded-sm" />
                </div>
              </div>
              
              {/* Title & Subtitle */}
              <div className="flex-1 min-w-0">
                <h1 className="font-display font-bold text-xl text-foreground leading-tight truncate">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-sm text-muted-foreground font-medium truncate animate-fade-in">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 ml-3">
            {/* Search */}
            <Button 
              variant="ghost" 
              size="icon"
              className="touch-target relative rounded-xl micro-bounce focus-ring"
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Notifications */}
            {showNotification && (
              <Button 
                variant="ghost" 
                size="icon"
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label={`Notifications (${notificationCount} unread)`}
              >
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-2xs font-bold animate-scale-in"
                  >
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </Badge>
                )}
              </Button>
            )}

            {/* Theme Toggle */}
            {mounted && (
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-warning animate-fade-in" />
                ) : (
                  <Moon className="h-5 w-5 text-primary animate-fade-in" />
                )}
              </Button>
            )}

            {/* Menu */}
            {showMenu && (
              <Button 
                variant="ghost" 
                size="icon"
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label="Menu"
              >
                <MenuIcon className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>

        {/* Enhanced Progress Bar for certain pages */}
        <div className="mt-3 h-1 bg-muted rounded-full overflow-hidden opacity-0 animate-fade-in" style={{ animationDelay: '200ms' }}>
          <div className="h-full bg-gradient-primary rounded-full w-1/3 animate-slide-in-left" style={{ animationDelay: '400ms' }} />
        </div>
      </div>
    </header>
  );
}