import { cn } from "@/lib/utils";
import { <PERSON>, Search, Menu as <PERSON>uIcon, Sun, Moon, LogOut, User, LogIn } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useTheme } from "next-themes";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  title: string;
  subtitle?: string;
  showNotification?: boolean;
  showMenu?: boolean;
  className?: string;
}

export function Header({
  title,
  subtitle,
  showNotification = true,
  showMenu = false,
  className
}: HeaderProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [notificationCount] = useState(3);
  const { user, isAuthenticated, signOut } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  return (
    <header className={cn(
      "sticky top-0 z-40 w-full border-b border-border-accent/50",
      "glass backdrop-blur-xl",
      className
    )}>
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-accent/5 pointer-events-none" />
      
      <div className="relative max-w-md mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo/Title Section */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3">
              {/* App Icon */}
              <div className="p-2 rounded-xl bg-gradient-primary shadow-soft">
                <div className="w-6 h-6 bg-white/90 rounded-lg flex items-center justify-center">
                  <div className="w-3 h-3 bg-primary rounded-sm" />
                </div>
              </div>
              
              {/* Title & Subtitle */}
              <div className="flex-1 min-w-0">
                <h1 className="font-display font-bold text-xl text-foreground leading-tight truncate">
                  {title}
                </h1>
                {subtitle && (
                  <p className="text-sm text-muted-foreground font-medium truncate animate-fade-in">
                    {subtitle}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 ml-3">
            {/* Search */}
            <Button 
              variant="ghost" 
              size="icon"
              className="touch-target relative rounded-xl micro-bounce focus-ring"
              aria-label="Search"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Notifications */}
            {showNotification && (
              <Button 
                variant="ghost" 
                size="icon"
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label={`Notifications (${notificationCount} unread)`}
              >
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-2xs font-bold animate-scale-in"
                  >
                    {notificationCount > 9 ? '9+' : notificationCount}
                  </Badge>
                )}
              </Button>
            )}

            {/* Theme Toggle */}
            {mounted && (
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label="Toggle theme"
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5 text-warning animate-fade-in" />
                ) : (
                  <Moon className="h-5 w-5 text-primary animate-fade-in" />
                )}
              </Button>
            )}

            {/* User Menu / Auth */}
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="touch-target relative rounded-xl micro-bounce focus-ring"
                    aria-label="User menu"
                  >
                    <User className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <div className="px-2 py-1.5">
                    <p className="text-sm font-medium">{user?.user_metadata?.full_name || 'User'}</p>
                    <p className="text-xs text-muted-foreground">{user?.email}</p>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate('/profile')}>
                    <User className="mr-2 h-4 w-4" />
                    Profil
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate('/history')}>
                    <Bell className="mr-2 h-4 w-4" />
                    Riwayat Assessment
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/login')}
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label="Login"
              >
                <LogIn className="h-5 w-5" />
              </Button>
            )}

            {/* Menu */}
            {showMenu && (
              <Button
                variant="ghost"
                size="icon"
                className="touch-target relative rounded-xl micro-bounce focus-ring"
                aria-label="Menu"
              >
                <MenuIcon className="h-5 w-5" />
              </Button>
            )}
          </div>
        </div>

        {/* Enhanced Progress Bar for certain pages */}
        <div className="mt-3 h-1 bg-muted rounded-full overflow-hidden opacity-0 animate-fade-in" style={{ animationDelay: '200ms' }}>
          <div className="h-full bg-gradient-primary rounded-full w-1/3 animate-slide-in-left" style={{ animationDelay: '400ms' }} />
        </div>
      </div>
    </header>
  );
}