import { useState } from "react";
import { Header } from "@/components/header";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { BookOpen, Video, FileText, ExternalLink, Clock, User, Download } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { EducationPopup } from "@/components/education-popup";
import { VideoModal } from "@/components/video-modal";

const educationContent = [
  {
    id: "1",
    title: "Memahami Kesehatan Mental pada Santri",
    description: "Panduan lengkap tentang kesehatan mental dan cara menjaganya dalam kehidupan pondok pesantren",
    type: "article",
    duration: "5 min",
    author: "Dr. <PERSON>",
    category: "Dasar"
  },
  {
    id: "2", 
    title: "Mengelola Stres Akademik",
    description: "Tips dan strategi untuk mengatasi tekanan belajar dan ujian di lingkungan pesantren",
    type: "article",
    duration: "7 min",
    author: "Prof. <PERSON>",
    category: "Stres"
  },
  {
    id: "3",
    title: "Video Playlist Kesehatan Mental",
    description: "Kumpulan video edukatif tentang manajemen stres dan kesehatan jiwa untuk santri",
    type: "video",
    duration: "Various",
    author: "Tim Ahli",
    category: "Video"
  },
  {
    id: "4",
    title: "Membangun Resiliensi Mental",
    description: "Cara mengembangkan ketahanan mental untuk menghadapi tantangan hidup",
    type: "article",
    duration: "6 min", 
    author: "Dr. Fatimah Rahman",
    category: "Resiliensi"
  },
  {
    id: "5",
    title: "Peran Dukungan Sosial dalam Kesehatan Mental",
    description: "Pentingnya hubungan sosial yang sehat dalam menjaga kesehatan mental santri",
    type: "article",
    duration: "4 min",
    author: "Ustad Mahmud",
    category: "Sosial"
  },
  {
    id: "6",
    title: "Mengenali Tanda-tanda Gangguan Mental",
    description: "Panduan untuk mengenali gejala awal gangguan mental dan kapan harus mencari bantuan",
    type: "guide",
    duration: "8 min",
    author: "Tim Psikolog",
    category: "Deteksi Dini"
  }
];

const getTypeIcon = (type: string) => {
  switch (type) {
    case "video":
      return <Video className="h-4 w-4" />;
    case "guide":
      return <FileText className="h-4 w-4" />;
    default:
      return <BookOpen className="h-4 w-4" />;
  }
};

const getTypeColor = (type: string) => {
  switch (type) {
    case "video":
      return "bg-red-100 text-red-700 dark:bg-red-950 dark:text-red-300";
    case "guide":
      return "bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300";
    default:
      return "bg-green-100 text-green-700 dark:bg-green-950 dark:text-green-300";
  }
};

export default function Education() {
  const navigate = useNavigate();
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  const handleContentClick = (content: any) => {
    if (content.type === "video" && content.id === "3") {
      navigate('/videos');
    } else {
      // For other content, you could navigate to detailed pages
      console.log('Opening content:', content.title);
    }
  };

  const handleVideoPopupClick = () => {
    navigate('/videos');
  };

  const handlePdfPopupClick = () => {
    // Here you would implement PDF download functionality
    console.log('PDF download requested');
    // You could open a PDF in a new tab or trigger download
  };

  return (
    <div className="min-h-screen bg-gradient-soft pb-20">
      <Header 
        title="Edukasi" 
        subtitle="Pelajari tentang kesehatan mental"
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Welcome Banner */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-primary text-white animate-fade-in">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-white/20 rounded-full">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <h2 className="font-display font-semibold text-lg mb-2">
                Pusat Edukasi
              </h2>
              <p className="text-white/90 text-sm leading-relaxed">
                Tingkatkan pemahaman Anda tentang kesehatan mental
              </p>
            </div>
          </div>
        </Card>

        {/* Categories */}
        <div className="space-y-4">
          <h3 className="font-display font-semibold text-foreground">
            Konten Edukasi
          </h3>
          
          {educationContent.map((content, index) => (
            <Card 
              key={content.id}
              className="p-4 shadow-soft border-0 bg-gradient-card hover:shadow-medium transition-all duration-200 cursor-pointer animate-slide-up"
              style={{ animationDelay: `${index * 100}ms` }}
              onClick={() => handleContentClick(content)}
            >
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${getTypeColor(content.type)}`}>
                    {getTypeIcon(content.type)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground text-sm leading-relaxed mb-1">
                      {content.title}
                    </h4>
                    <p className="text-muted-foreground text-xs leading-relaxed mb-2">
                      {content.description}
                    </p>
                    
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {content.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {content.author}
                      </div>
                    </div>
                  </div>
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                </div>

                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="text-xs">
                    {content.category}
                  </Badge>
                  
                  {content.type === "video" && content.id === "3" && (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-primary hover:text-primary/80"
                    >
                      Tonton Video
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Quick Access with Popup */}
        <Card className="p-4 shadow-soft border-0 bg-gradient-card">
          <h4 className="font-medium text-foreground mb-3">Akses Cepat</h4>
          <div className="flex flex-col gap-3">
            <div className="grid grid-cols-2 gap-3">
              <Button 
                variant="outline" 
                className="flex items-center gap-2 justify-start h-auto p-3"
                onClick={() => navigate('/videos')}
              >
                <Video className="h-4 w-4 text-primary" />
                <span className="text-xs">Video Playlist</span>
              </Button>
              <Button 
                variant="outline" 
                className="flex items-center gap-2 justify-start h-auto p-3"
                onClick={handlePdfPopupClick}
              >
                <FileText className="h-4 w-4 text-primary" />
                <span className="text-xs">Panduan PDF</span>
              </Button>
            </div>
            
            {/* Education Popup */}
            <div className="flex justify-center pt-2">
              <EducationPopup 
                onVideoClick={handleVideoPopupClick}
                onPdfClick={handlePdfPopupClick}
              />
            </div>
          </div>
        </Card>
      </div>
      
      <BottomNav />
      
      {/* Video Modal */}
      <VideoModal 
        video={selectedVideo}
        isOpen={isVideoModalOpen}
        onClose={() => {
          setIsVideoModalOpen(false);
          setSelectedVideo(null);
        }}
      />
    </div>
  );
}