import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  badge?: string;
  gradient: string;
  className?: string;
  delay?: number;
}

export function FeatureCard({
  icon: Icon,
  title,
  description,
  badge,
  gradient,
  className,
  delay = 0
}: FeatureCardProps) {
  return (
    <Card className={cn(
      "group relative overflow-hidden border-0 p-6",
      "shadow-soft hover:shadow-interactive transition-all duration-500",
      "hover:scale-[1.02] hover:-translate-y-1 card-interactive",
      "bg-gradient-card animate-fade-in",
      className
    )}
    style={{ animationDelay: `${delay}ms` }}
    >
      {/* Background Gradient */}
      <div className={cn(
        "absolute top-0 right-0 w-24 h-24 rounded-full opacity-10",
        "transition-all duration-500 group-hover:opacity-20 group-hover:scale-110",
        gradient
      )} />
      
      {/* Icon */}
      <div className={cn(
        "inline-flex p-3 rounded-2xl mb-4 shadow-medium",
        "bg-white/80 dark:bg-black/20 border border-white/50",
        "transition-all duration-300 group-hover:scale-110 group-hover:shadow-glow",
        gradient.replace('bg-gradient-to-br', 'group-hover:bg-gradient-to-br')
      )}>
        <Icon className="h-6 w-6 text-primary group-hover:text-white transition-colors duration-300" />
      </div>
      
      {/* Content */}
      <div className="space-y-2">
        {badge && (
          <Badge variant="secondary" className="text-2xs px-2 py-1 mb-2">
            {badge}
          </Badge>
        )}
        
        <h3 className="font-display font-bold text-lg text-foreground leading-tight">
          {title}
        </h3>
        
        <p className="text-muted-foreground text-sm leading-relaxed">
          {description}
        </p>
      </div>
      
      {/* Hover Overlay */}
      <div className={cn(
        "absolute inset-0 rounded-xl opacity-0 group-hover:opacity-10",
        "transition-opacity duration-500 pointer-events-none",
        gradient
      )} />
    </Card>
  );
}