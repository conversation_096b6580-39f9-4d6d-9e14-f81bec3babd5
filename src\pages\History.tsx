import { Header } from "@/components/header";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, TrendingUp, CheckCircle, AlertCircle, Info } from "lucide-react";

const assessmentHistory = [
  {
    id: "1",
    title: "General Self-Efficacy Scale",
    date: "15 Agustus 2024",
    score: "32/40",
    category: "Tinggi",
    status: "completed",
    type: "gse"
  },
  {
    id: "2", 
    title: "DASS-42",
    date: "10 Agustus 2024",
    score: "18/126",
    category: "Normal",
    status: "completed",
    type: "dass42"
  },
  {
    id: "3",
    title: "Mental Health Knowledge Questionnaire",
    date: "5 Agustus 2024",
    score: "12/15",
    category: "Baik",
    status: "completed",
    type: "mhkq"
  },
  {
    id: "4",
    title: "Multidimensional Scale of Perceived Social Support",
    date: "1 Agustus 2024",
    score: "72/84",
    category: "Tinggi",
    status: "completed",
    type: "mscs"
  }
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-4 w-4 text-success" />;
    case "in-progress":
      return <AlertCircle className="h-4 w-4 text-warning" />;
    default:
      return <Info className="h-4 w-4 text-muted-foreground" />;
  }
};

const getCategoryColor = (category: string) => {
  switch (category.toLowerCase()) {
    case "tinggi":
      return "bg-success/10 text-success border-success/20";
    case "normal":
    case "baik":
      return "bg-primary/10 text-primary border-primary/20";
    case "sedang":
      return "bg-warning/10 text-warning border-warning/20";
    case "rendah":
      return "bg-destructive/10 text-destructive border-destructive/20";
    default:
      return "bg-muted/10 text-muted-foreground border-muted/20";
  }
};

export default function History() {
  return (
    <div className="min-h-screen bg-gradient-soft pb-20">
      <Header 
        title="Riwayat Assessment" 
        subtitle="Lihat hasil assessment sebelumnya"
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Summary Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-gradient-primary shadow-soft">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="font-display font-semibold text-foreground mb-1">
                Progress Anda
              </h3>
              <p className="text-muted-foreground text-sm mb-2">
                Total 4 assessment telah diselesaikan
              </p>
              <div className="flex gap-2">
                <Badge variant="secondary" className="text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  Bulan ini: 4
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        {/* History List */}
        <div className="space-y-3">
          <h3 className="font-display font-semibold text-foreground">
            Riwayat Assessment
          </h3>

          {assessmentHistory.map((assessment, index) => (
            <Card 
              key={assessment.id}
              className="p-4 shadow-soft border-0 bg-gradient-card animate-slide-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground text-sm leading-relaxed">
                      {assessment.title}
                    </h4>
                    <p className="text-muted-foreground text-xs mt-1">
                      {assessment.date}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(assessment.status)}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-center">
                      <div className="text-lg font-bold text-foreground">
                        {assessment.score}
                      </div>
                      <div className="text-xs text-muted-foreground">Skor</div>
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${getCategoryColor(assessment.category)}`}
                    >
                      {assessment.category}
                    </Badge>
                  </div>
                  
                  <Button 
                    variant="ghost" 
                    size="sm"
                    className="text-primary hover:text-primary/80"
                  >
                    Lihat Detail
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State for new users */}
        {assessmentHistory.length === 0 && (
          <Card className="p-8 text-center shadow-soft border-0 bg-gradient-card">
            <div className="p-4 rounded-full bg-muted/20 w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Calendar className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="font-display font-semibold text-foreground mb-2">
              Belum Ada Riwayat
            </h3>
            <p className="text-muted-foreground text-sm mb-4">
              Mulai dengan assessment pertama Anda untuk melihat riwayat di sini
            </p>
            <Button className="bg-gradient-primary">
              Mulai Assessment
            </Button>
          </Card>
        )}
      </div>
      
      <BottomNav />
    </div>
  );
}