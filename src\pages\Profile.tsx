import { Header } from "@/components/header";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { User, MapPin, Calendar, BookOpen, Settings, HelpCircle, LogOut, History } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function Profile() {
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen bg-gradient-soft pb-20">
      <Header 
        title="Profil" 
        subtitle="Informasi dan pengaturan akun"
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Profile Info */}
        <Card className="p-6 shadow-soft border-0 bg-gradient-card animate-fade-in">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16 shadow-medium">
              <AvatarFallback className="bg-gradient-primary text-white text-lg font-semibold">
                AS
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <h3 className="font-display font-semibold text-lg text-foreground">
                Ahmad Santoso
              </h3>
              <p className="text-muted-foreground text-sm">
                Santri Kelas 3 Aliyah
              </p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="text-xs">
                  <MapPin className="h-3 w-3 mr-1" />
                  Pondok Al-Hikmah
                </Badge>
                <Badge variant="outline" className="text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  2006
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-3">
          <Card className="p-4 text-center shadow-soft border-0 bg-gradient-card animate-slide-up">
            <div className="text-2xl font-bold text-primary">12</div>
            <div className="text-xs text-muted-foreground">Assessment</div>
          </Card>
          <Card className="p-4 text-center shadow-soft border-0 bg-gradient-card animate-slide-up" style={{ animationDelay: '100ms' }}>
            <div className="text-2xl font-bold text-success">8</div>
            <div className="text-xs text-muted-foreground">Selesai</div>
          </Card>
          <Card className="p-4 text-center shadow-soft border-0 bg-gradient-card animate-slide-up" style={{ animationDelay: '200ms' }}>
            <div className="text-2xl font-bold text-accent">15</div>
            <div className="text-xs text-muted-foreground">Artikel Dibaca</div>
          </Card>
        </div>

        {/* Menu Items */}
        <div className="space-y-3">
          <MenuCard 
            icon={History} 
            title="Riwayat Assessment" 
            subtitle="Lihat hasil assessment sebelumnya"
            onClick={() => navigate('/history')}
          />
          <MenuCard 
            icon={Settings} 
            title="Pengaturan" 
            subtitle="Kelola preferensi dan notifikasi"
            onClick={() => {}}
          />
          <MenuCard 
            icon={HelpCircle} 
            title="Bantuan & FAQ" 
            subtitle="Panduan penggunaan aplikasi"
            onClick={() => {}}
          />
          <MenuCard 
            icon={LogOut} 
            title="Keluar" 
            subtitle="Logout dari akun Anda"
            onClick={() => {}}
            variant="destructive"
          />
        </div>
      </div>
      
      <BottomNav />
    </div>
  );
}

interface MenuCardProps {
  icon: React.ElementType;
  title: string;
  subtitle: string;
  onClick: () => void;
  variant?: "default" | "destructive";
}

function MenuCard({ icon: Icon, title, subtitle, onClick, variant = "default" }: MenuCardProps) {
  return (
    <Card 
      className="p-4 shadow-soft border-0 bg-gradient-card hover:shadow-medium transition-all duration-200 cursor-pointer animate-fade-in"
      onClick={onClick}
    >
      <div className="flex items-center gap-4">
        <div className={`p-3 rounded-xl shadow-soft ${
          variant === "destructive" 
            ? "bg-red-100 dark:bg-red-950" 
            : "bg-primary-soft"
        }`}>
          <Icon className={`h-5 w-5 ${
            variant === "destructive" 
              ? "text-destructive" 
              : "text-primary"
          }`} />
        </div>
        
        <div className="flex-1">
          <h4 className="font-medium text-foreground">{title}</h4>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
      </div>
    </Card>
  );
}