import { useState, useEffect } from "react";
import { Header } from "@/components/header";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { CheckCircle, ArrowLeft, Home, Save } from "lucide-react";
import { useAssessmentSession } from "@/hooks/use-assessment-session";
import { useToast } from "@/hooks/use-toast";

const mhkqQuestions = [
  "Masalah kesehatan mental dapat mempengaruhi siapa saja, tanpa memandang usia, jenis kelamin, atau latar belakang sosial ekonomi",
  "Gangguan mental adalah hasil dari kelemahan karakter atau kurangnya kemauan",
  "Terapi psikologi dapat membantu mengatasi berbagai masalah kesehatan mental",
  "Obat-obatan psikiatri selalu diperlukan untuk mengobati gangguan mental",
  "Orang dengan gangguan mental lebih cenderung melakukan kekerasan daripada orang normal",
  "Stres yang berkepanjangan dapat memicu atau memperburuk masalah kesehatan mental",
  "Gangguan depresi hanya dialami oleh orang dewasa",
  "Dukungan sosial dari keluarga dan teman dapat membantu pemulihan kesehatan mental",
  "Orang dengan gangguan mental tidak dapat bekerja atau berfungsi normal dalam masyarakat",
  "Gaya hidup sehat (olahraga, nutrisi, tidur cukup) dapat membantu menjaga kesehatan mental",
  "Gangguan kecemasan adalah hal yang normal dan tidak perlu diobati",
  "Terapi kelompok dapat efektif untuk beberapa jenis gangguan mental",
  "Orang dengan riwayat keluarga gangguan mental pasti akan mengalami gangguan yang sama",
  "Pencegahan bunuh diri dapat dilakukan dengan mengenali tanda-tanda peringatan dini",
  "Stigma masyarakat dapat memperburuk kondisi orang dengan gangguan mental"
];

const scaleLabels = [
  "Salah",
  "Benar"
];

// Correct answers (0 = Salah, 1 = Benar)
const correctAnswers = [1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1];

export default function MHKQAssessment() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const { session, createSession, saveAnswer, completeSession, isLoading } = useAssessmentSession('mhkq');

  useEffect(() => {
    if (!session) {
      createSession();
    }
  }, []);

  const progress = ((currentQuestion + (selectedAnswer !== null ? 1 : 0)) / mhkqQuestions.length) * 100;

  const handleAnswer = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = async () => {
    if (selectedAnswer === null || !session) return;

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = selectedAnswer;
    setAnswers(newAnswers);

    // Save answer to database
    await saveAnswer(`mhkq_q${currentQuestion + 1}`, selectedAnswer);
    
    setSelectedAnswer(null);

    if (currentQuestion < mhkqQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const result = calculateResult(newAnswers);
      await completeSession(result.totalScore, {
        correctAnswers: result.correctCount,
        percentage: result.percentage
      });
      setIsCompleted(true);
    }
  };

  const handleSaveProgress = async () => {
    if (selectedAnswer !== null && session) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      await saveAnswer(`mhkq_q${currentQuestion + 1}`, selectedAnswer);
      
      toast({
        title: "Progress Tersimpan",
        description: "Jawaban Anda telah disimpan.",
      });
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(answers[currentQuestion - 1] ?? null);
    }
  };

  const calculateResult = (answersArray = answers) => {
    const correctCount = answersArray.reduce((count, answer, index) => {
      return count + (answer === correctAnswers[index] ? 1 : 0);
    }, 0);
    
    const totalScore = correctCount;
    const percentage = Math.round((correctCount / mhkqQuestions.length) * 100);
    
    let category = "";
    if (percentage >= 80) category = "Sangat Baik";
    else if (percentage >= 60) category = "Baik";
    else if (percentage >= 40) category = "Cukup";
    else category = "Perlu Ditingkatkan";

    return { totalScore, correctCount, percentage, category };
  };

  if (isCompleted) {
    const result = calculateResult();
    
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header 
          title="Hasil Assessment" 
          subtitle="Mental Health Knowledge Questionnaire"
        />
        
        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-gradient-primary w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            
            <h2 className="font-display font-bold text-2xl text-foreground mb-2">
              Assessment Selesai!
            </h2>
            <p className="text-muted-foreground mb-6">
              Terima kasih telah menyelesaikan MHKQ Assessment
            </p>

            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-primary-soft">
                <div className="text-3xl font-bold text-primary mb-1">
                  {result.correctCount}/{mhkqQuestions.length}
                </div>
                <div className="text-sm text-muted-foreground">Jawaban Benar</div>
                <div className="text-xl font-semibold text-primary mt-1">
                  {result.percentage}%
                </div>
              </div>

              <div className="p-4 rounded-lg bg-secondary">
                <div className="text-lg font-semibold text-foreground mb-1">
                  Tingkat Pengetahuan: {result.category}
                </div>
                <div className="text-sm text-muted-foreground">
                  {result.category === "Sangat Baik" && "Pengetahuan kesehatan mental Anda sangat baik"}
                  {result.category === "Baik" && "Pengetahuan kesehatan mental Anda cukup baik"}
                  {result.category === "Cukup" && "Pengetahuan kesehatan mental Anda perlu ditingkatkan"}
                  {result.category === "Perlu Ditingkatkan" && "Disarankan untuk mempelajari lebih lanjut tentang kesehatan mental"}
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex-1"
              >
                Kembali ke Beranda
              </Button>
              <Button 
                onClick={() => navigate('/assessment')}
                className="flex-1 bg-gradient-primary"
              >
                Assessment Lain
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-soft">
      <Header 
        title="MHKQ Assessment" 
        subtitle={`Pertanyaan ${currentQuestion + 1} dari ${mhkqQuestions.length}`}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <h2 className="font-display font-semibold text-lg text-foreground mb-6 leading-relaxed">
            {mhkqQuestions[currentQuestion]}
          </h2>

          <div className="space-y-3">
            {scaleLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(index)}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedAnswer === index
                    ? 'border-primary bg-primary-soft shadow-soft'
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index 
                      ? 'border-primary bg-primary' 
                      : 'border-muted-foreground'
                  }`}>
                    {selectedAnswer === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="font-medium text-foreground">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Navigation */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Sebelumnya
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 bg-gradient-primary"
            >
              {currentQuestion === mhkqQuestions.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>

          {/* Progress controls */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Beranda
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Simpan Progress
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}