import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Video, FileText, ChevronUp, Download } from "lucide-react";
import { cn } from "@/lib/utils";

interface EducationPopupProps {
  onVideoClick: () => void;
  onPdfClick: () => void;
  className?: string;
}

export function EducationPopup({ onVideoClick, onPdfClick, className }: EducationPopupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div ref={popupRef} className={cn("relative", className)}>
      {/* Popup Menu */}
      {isOpen && (
        <Card className={cn(
          "absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2",
          "w-64 p-2 shadow-glow border-0 bg-gradient-card animate-slide-up z-50"
        )}>
          <div className="space-y-2">
            {/* Video Section */}
            <Button
              variant="ghost"
              onClick={() => {
                onVideoClick();
                setIsOpen(false);
              }}
              className="w-full justify-start h-auto p-3 hover:bg-primary/5 group"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-red-100 text-red-700 dark:bg-red-950 dark:text-red-300 group-hover:scale-110 transition-transform">
                  <Video className="h-4 w-4" />
                </div>
                <div className="text-left">
                  <p className="font-medium text-sm text-foreground">Video Pembelajaran</p>
                  <p className="text-xs text-muted-foreground">Tonton video edukatif</p>
                </div>
              </div>
            </Button>

            {/* PDF Section */}
            <Button
              variant="ghost"
              onClick={() => {
                onPdfClick();
                setIsOpen(false);
              }}
              className="w-full justify-start h-auto p-3 hover:bg-primary/5 group"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300 group-hover:scale-110 transition-transform">
                  <FileText className="h-4 w-4" />
                </div>
                <div className="text-left">
                  <p className="font-medium text-sm text-foreground">Materi PDF</p>
                  <p className="text-xs text-muted-foreground">Download panduan lengkap</p>
                </div>
              </div>
            </Button>

            <div className="pt-2 border-t border-border/50">
              <p className="text-xs text-muted-foreground text-center">
                Pilih jenis konten edukasi
              </p>
            </div>
          </div>

          {/* Arrow pointing down */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 -translate-y-1">
            <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-card"></div>
          </div>
        </Card>
      )}

      {/* Trigger Button */}
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="outline"
        className={cn(
          "border-primary/20 text-primary hover:bg-primary/5 transition-all duration-300",
          "relative overflow-hidden group",
          isOpen && "bg-primary/5"
        )}
      >
        <div className="flex items-center gap-2">
          <ChevronUp className={cn(
            "h-4 w-4 transition-transform duration-300",
            isOpen ? "rotate-0" : "rotate-180"
          )} />
          <span className="font-medium">Konten Edukasi</span>
          <Download className="h-4 w-4 group-hover:animate-bounce" />
        </div>
        
        {/* Hover effect */}
        <div className="absolute inset-0 bg-gradient-primary opacity-0 group-hover:opacity-5 transition-opacity duration-300" />
      </Button>
    </div>
  );
}