import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ExternalLink, Play, Clock, Eye, X } from "lucide-react";

interface Video {
  id: string;
  title: string;
  url: string;
  thumbnail: string;
  duration: string;
  views: string;
  category: string;
}

interface VideoModalProps {
  video: Video | null;
  isOpen: boolean;
  onClose: () => void;
}

export function VideoModal({ video, isOpen, onClose }: VideoModalProps) {
  const [showEmbedded, setShowEmbedded] = useState(false);

  if (!video) return null;

  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = url.includes('youtube.com') 
      ? url.split('v=')[1]?.split('&')[0]
      : url.split('youtu.be/')[1]?.split('?')[0];
    return `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
  };

  const handleWatchOnYouTube = () => {
    window.open(video.url, '_blank');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-full mx-4 p-0 overflow-hidden bg-gradient-card border-0 shadow-glow">
        {/* Header */}
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 pr-4">
              <DialogTitle className="text-xl font-display font-bold text-foreground leading-tight">
                {video.title}
              </DialogTitle>
              <div className="flex items-center gap-4 mt-3">
                <Badge className="bg-primary/10 text-primary border-primary/20">
                  {video.category}
                </Badge>
                <div className="flex items-center gap-2 text-muted-foreground text-sm">
                  <Clock className="h-4 w-4" />
                  {video.duration}
                </div>
                <div className="flex items-center gap-2 text-muted-foreground text-sm">
                  <Eye className="h-4 w-4" />
                  {video.views} tayangan
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        {/* Video Content */}
        <div className="px-6">
          {showEmbedded ? (
            <div className="relative w-full aspect-video rounded-xl overflow-hidden shadow-medium">
              <iframe
                src={getYouTubeEmbedUrl(video.url)}
                title={video.title}
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          ) : (
            <div className="relative w-full aspect-video rounded-xl overflow-hidden shadow-medium group cursor-pointer">
              <img 
                src={video.thumbnail}
                alt={video.title}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                onError={(e) => {
                  e.currentTarget.src = '/placeholder.svg';
                }}
              />
              <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                <Button
                  size="icon"
                  onClick={() => setShowEmbedded(true)}
                  className="h-20 w-20 rounded-full bg-white/95 hover:bg-white text-primary shadow-glow hover:scale-110 transition-all duration-300"
                >
                  <Play className="h-10 w-10 ml-1" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="p-6 pt-4 flex flex-col sm:flex-row gap-3">
          {!showEmbedded && (
            <Button
              onClick={() => setShowEmbedded(true)}
              className="flex-1 bg-gradient-primary hover:shadow-glow transition-all duration-300 font-semibold"
              size="lg"
            >
              <Play className="h-5 w-5 mr-2" />
              Tonton di Sini
            </Button>
          )}
          
          <Button
            variant="outline" 
            onClick={handleWatchOnYouTube}
            className="flex-1 border-primary/20 text-primary hover:bg-primary/5 font-semibold"
            size="lg"
          >
            <ExternalLink className="h-5 w-5 mr-2" />
            Buka di YouTube
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}