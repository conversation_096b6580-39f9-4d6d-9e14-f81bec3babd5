import { useState, useEffect } from "react";
import { Header } from "@/components/header";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { CheckCircle, ArrowLeft, Home, Save } from "lucide-react";
import { useAssessmentSession } from "@/hooks/use-assessment-session";
import { useToast } from "@/hooks/use-toast";

const pddQuestions = [
  "Kebanyakan orang percaya bahwa seseorang yang pernah dirawat di rumah sakit jiwa sama tidak dapat dipercayanya seperti orang normal",
  "Kebanyakan orang akan menerima seorang mantan pasien rumah sakit jiwa sebagai teman dekat",
  "Kebanyakan orang percaya bahwa seseorang yang pernah dirawat di rumah sakit jiwa sama pintarnya dengan rata-rata orang",
  "Kebanyakan orang percaya bahwa mantan pasien rumah sakit jiwa tidak dapat dipercaya untuk menjaga anak-anak",
  "Kebanyakan orang akan menghindari seseorang yang pernah dirawat di rumah sakit jiwa",
  "Kebanyakan orang tidak akan mempekerjakan seseorang yang pernah dirawat di rumah sakit jiwa untuk mengurus anak-anak mereka",
  "Kebanyakan orang akan menerima seorang mantan pasien rumah sakit jiwa sebagai tetangga",
  "Kebanyakan orang percaya bahwa mantan pasien rumah sakit jiwa sama dapat diandalkannya dengan warga rata-rata",
  "Kebanyakan orang akan enggan berdekatan dengan seseorang yang pernah dirawat di rumah sakit jiwa",
  "Kebanyakan majikan akan mempekerjakan mantan pasien rumah sakit jiwa jika mereka memenuhi syarat untuk pekerjaan tersebut",
  "Kebanyakan orang di komunitas saya akan menganggap seorang mantan pasien rumah sakit jiwa sama seperti orang lain",
  "Kebanyakan orang akan memperlakukan mantan pasien rumah sakit jiwa seperti orang normal"
];

const scaleLabels = [
  "Sangat Setuju",
  "Setuju",
  "Agak Setuju",
  "Agak Tidak Setuju",
  "Tidak Setuju",
  "Sangat Tidak Setuju"
];

export default function PDDAssessment() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const { session, createSession, saveAnswer, completeSession, isLoading } = useAssessmentSession('pdd');

  useEffect(() => {
    if (!session) {
      createSession();
    }
  }, []);

  const progress = ((currentQuestion + (selectedAnswer !== null ? 1 : 0)) / pddQuestions.length) * 100;

  const handleAnswer = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = async () => {
    if (selectedAnswer === null || !session) return;

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = selectedAnswer;
    setAnswers(newAnswers);

    // Save answer to database
    await saveAnswer(`pdd_q${currentQuestion + 1}`, selectedAnswer);
    
    setSelectedAnswer(null);

    if (currentQuestion < pddQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const result = calculateResult(newAnswers);
      await completeSession(result.totalScore, {
        averageScore: result.averageScore,
        category: result.category
      });
      setIsCompleted(true);
    }
  };

  const handleSaveProgress = async () => {
    if (selectedAnswer !== null && session) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      await saveAnswer(`pdd_q${currentQuestion + 1}`, selectedAnswer);
      
      toast({
        title: "Progress Tersimpan",
        description: "Jawaban Anda telah disimpan.",
      });
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(answers[currentQuestion - 1] ?? null);
    }
  };

  const calculateResult = (answersArray = answers) => {
    // Reverse scoring for positive items (items 1, 2, 6, 9, 10, 11)
    const reverseScoreItems = [1, 2, 6, 9, 10, 11]; // 0-indexed
    
    let totalScore = 0;
    answersArray.forEach((answer, index) => {
      if (reverseScoreItems.includes(index)) {
        // Reverse score: 0->5, 1->4, 2->3, 3->2, 4->1, 5->0
        totalScore += (5 - answer);
      } else {
        totalScore += answer;
      }
    });
    
    const averageScore = totalScore / pddQuestions.length;
    
    let category = "";
    if (averageScore <= 2) category = "Stigma Rendah";
    else if (averageScore <= 3.5) category = "Stigma Sedang";
    else category = "Stigma Tinggi";

    return { totalScore, averageScore: Math.round(averageScore * 10) / 10, category };
  };

  if (isCompleted) {
    const result = calculateResult();
    
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header 
          title="Hasil Assessment" 
          subtitle="Perceived Devaluation-Discrimination Scale"
        />
        
        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-gradient-primary w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            
            <h2 className="font-display font-bold text-2xl text-foreground mb-2">
              Assessment Selesai!
            </h2>
            <p className="text-muted-foreground mb-6">
              Terima kasih telah menyelesaikan PDD Assessment
            </p>

            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-primary-soft">
                <div className="text-3xl font-bold text-primary mb-1">
                  {result.averageScore}/6
                </div>
                <div className="text-sm text-muted-foreground">Skor Rata-rata</div>
              </div>

              <div className="p-4 rounded-lg bg-secondary">
                <div className="text-lg font-semibold text-foreground mb-1">
                  Persepsi Stigma: {result.category}
                </div>
                <div className="text-sm text-muted-foreground">
                  {result.category === "Stigma Rendah" && "Anda memiliki persepsi stigma yang rendah terhadap masalah kesehatan mental"}
                  {result.category === "Stigma Sedang" && "Anda memiliki persepsi stigma yang moderat"}
                  {result.category === "Stigma Tinggi" && "Anda memiliki persepsi stigma yang tinggi, perlu meningkatkan pemahaman"}
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex-1"
              >
                Kembali ke Beranda
              </Button>
              <Button 
                onClick={() => navigate('/assessment')}
                className="flex-1 bg-gradient-primary"
              >
                Assessment Lain
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-soft">
      <Header 
        title="PDD Assessment" 
        subtitle={`Pertanyaan ${currentQuestion + 1} dari ${pddQuestions.length}`}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <h2 className="font-display font-semibold text-lg text-foreground mb-6 leading-relaxed">
            {pddQuestions[currentQuestion]}
          </h2>

          <div className="space-y-3">
            {scaleLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(index)}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedAnswer === index
                    ? 'border-primary bg-primary-soft shadow-soft'
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index 
                      ? 'border-primary bg-primary' 
                      : 'border-muted-foreground'
                  }`}>
                    {selectedAnswer === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="font-medium text-foreground">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Navigation */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Sebelumnya
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 bg-gradient-primary"
            >
              {currentQuestion === pddQuestions.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>

          {/* Progress controls */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Beranda
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Simpan Progress
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}