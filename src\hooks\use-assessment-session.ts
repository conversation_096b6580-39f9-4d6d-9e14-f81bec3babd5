import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface AssessmentSession {
  id?: string;
  instrument: string;
  status: string;
  user_id: string;
  started_at: string;
  completed_at?: string | null;
  score_total?: number | null;
  score_breakdown?: any;
  created_at?: string;
  updated_at?: string;
}

export interface AssessmentAnswer {
  session_id: string;
  question_id: string;
  value: string;
}

export function useAssessmentSession(instrument: string) {
  const [session, setSession] = useState<AssessmentSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState<Record<string, any>>({});
  const { toast } = useToast();

  // Create new assessment session
  const createSession = async () => {
    setIsLoading(true);
    try {
      const userId = 'user_' + Math.random().toString(36).substr(2, 9); // Mock user ID for now
      
      const { data, error } = await supabase
        .from('assessment_sessions')
        .insert({
          instrument,
          status: 'in_progress',
          user_id: userId,
          started_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      setSession(data);
      return data;
    } catch (error) {
      console.error('Error creating session:', error);
      toast({
        title: "Kesalahan",
        description: "Gagal memulai assessment. Silakan coba lagi.",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Save answer for current question
  const saveAnswer = async (questionId: string, value: string | number) => {
    if (!session) return;

    try {
      const { error } = await supabase
        .from('assessment_answers')
        .upsert({
          session_id: session.id!,
          question_id: questionId,
          value: value.toString(),
        }, {
          onConflict: 'session_id,question_id'
        });

      if (error) throw error;

      // Update local progress
      setProgress(prev => ({
        ...prev,
        [questionId]: value
      }));

    } catch (error) {
      console.error('Error saving answer:', error);
      toast({
        title: "Kesalahan",
        description: "Gagal menyimpan progress. Silakan coba lagi.",
        variant: "destructive"
      });
    }
  };

  // Complete assessment session
  const completeSession = async (totalScore: number, scoreBreakdown?: any) => {
    if (!session) return;

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('assessment_sessions')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          score_total: totalScore,
          score_breakdown: scoreBreakdown,
        })
        .eq('id', session.id!)
        .select()
        .single();

      if (error) throw error;

      setSession(data);
      toast({
        title: "Assessment Selesai",
        description: "Hasil assessment telah tersimpan.",
        variant: "default"
      });
      
      return data;
    } catch (error) {
      console.error('Error completing session:', error);
      toast({
        title: "Kesalahan",
        description: "Gagal menyimpan hasil assessment.",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Load existing session progress
  const loadProgress = async (sessionId: string) => {
    try {
      const { data, error } = await supabase
        .from('assessment_answers')
        .select('*')
        .eq('session_id', sessionId);

      if (error) throw error;

      const progressMap = data.reduce((acc, answer) => {
        acc[answer.question_id] = answer.value;
        return acc;
      }, {} as Record<string, any>);

      setProgress(progressMap);
      return progressMap;
    } catch (error) {
      console.error('Error loading progress:', error);
      return {};
    }
  };

  return {
    session,
    progress,
    isLoading,
    createSession,
    saveAnswer,
    completeSession,
    loadProgress,
  };
}