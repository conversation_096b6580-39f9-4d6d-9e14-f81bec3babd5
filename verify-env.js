#!/usr/bin/env node

/**
 * Supabase Environment Verification Script
 * This script verifies that all required Supabase environment variables are properly configured
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Required environment variables for Supabase
const REQUIRED_ENV_VARS = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
];

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

console.log(`${colors.blue}🔍 Supabase Environment Verification${colors.reset}`);
console.log('=====================================\n');

// Check if .env file exists
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  console.error(`${colors.red}❌ .env file not found!${colors.reset}`);
  console.log(`Please create a .env file in the project root with the following variables:`);
  console.log(`\n${colors.yellow}VITE_SUPABASE_URL=your_supabase_url`);
  console.log(`VITE_SUPABASE_ANON_KEY=your_supabase_anon_key${colors.reset}\n`);
  process.exit(1);
}

// Load environment variables
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

// Parse .env file
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    const value = valueParts.join('=').trim();
    envVars[key.trim()] = value.replace(/^["']|["']$/g, ''); // Remove quotes
  }
});

// Check required variables
let allValid = true;
const results = [];

REQUIRED_ENV_VARS.forEach(varName => {
  const value = envVars[varName];
  if (!value || value === '') {
    results.push(`${colors.red}❌ ${varName}: Missing or empty${colors.reset}`);
    allValid = false;
  } else if (value.includes('your_') || value.includes('placeholder')) {
    results.push(`${colors.yellow}⚠️  ${varName}: Using placeholder value${colors.reset}`);
    allValid = false;
  } else {
    results.push(`${colors.green}✅ ${varName}: Configured${colors.reset}`);
  }
});

// Display results
console.log('Environment Variables:');
results.forEach(result => console.log(`  ${result}`));

// Validate Supabase URL format
const supabaseUrl = envVars.VITE_SUPABASE_URL;
if (supabaseUrl && !supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('supabase.in')) {
  console.log(`\n${colors.yellow}⚠️  Warning: VITE_SUPABASE_URL doesn't appear to be a valid Supabase URL${colors.reset}`);
  console.log(`Expected format: https://[project-ref].supabase.co`);
  allValid = false;
}

// Validate Supabase key format
const supabaseKey = envVars.VITE_SUPABASE_ANON_KEY;
if (supabaseKey && !supabaseKey.startsWith('eyJ')) {
  console.log(`\n${colors.yellow}⚠️  Warning: VITE_SUPABASE_ANON_KEY doesn't appear to be a valid JWT token${colors.reset}`);
  console.log(`Expected format: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`);
  allValid = false;
}

// Final result
console.log('\n' + '='.repeat(37));
if (allValid) {
  console.log(`${colors.green}🎉 All Supabase environment variables are properly configured!${colors.reset}`);
  console.log(`\nYou can now start your development server:`);
  console.log(`${colors.blue}npm run dev${colors.reset}`);
} else {
  console.log(`${colors.red}❌ Configuration issues found!${colors.reset}`);
  console.log(`\nPlease check your .env file and ensure all variables are correctly set.`);
  console.log(`Refer to .env.example for the required format.`);
  process.exit(1);
}