import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Clock, Eye, BookOpen } from "lucide-react";
import { cn } from "@/lib/utils";

interface EducationCardProps {
  title: string;
  description: string;
  duration: string;
  views: number;
  category: string;
  thumbnail?: string;
  isNew?: boolean;
  className?: string;
  delay?: number;
  onPlay: () => void;
}

export function EducationCard({
  title,
  description,
  duration,
  views,
  category,
  thumbnail,
  isNew = false,
  className,
  delay = 0,
  onPlay
}: EducationCardProps) {
  return (
    <Card className={cn(
      "group relative overflow-hidden border-0 shadow-soft",
      "hover:shadow-interactive transition-all duration-500",
      "hover:scale-[1.02] card-interactive animate-fade-in",
      "bg-gradient-card",
      className
    )}
    style={{ animationDelay: `${delay}ms` }}
    >
      {/* Thumbnail/Visual Section */}
      <div className="relative h-40 bg-gradient-primary overflow-hidden">
        {thumbnail ? (
          <img 
            src={thumbnail} 
            alt={title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />
        ) : (
          <div className="w-full h-full bg-gradient-primary flex items-center justify-center">
            <BookOpen className="h-12 w-12 text-white/80" />
          </div>
        )}
        
        {/* Play Button Overlay */}
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button
            size="icon"
            onClick={onPlay}
            className="h-16 w-16 rounded-full bg-white/90 hover:bg-white text-primary shadow-glow"
            aria-label={`Play ${title}`}
          >
            <Play className="h-8 w-8 ml-1" />
          </Button>
        </div>
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex gap-2">
          <Badge className="bg-black/50 text-white text-2xs">
            {category}
          </Badge>
          {isNew && (
            <Badge variant="destructive" className="text-2xs animate-pulse-soft">
              Baru
            </Badge>
          )}
        </div>
        
        {/* Duration */}
        <div className="absolute bottom-3 right-3">
          <Badge className="bg-black/70 text-white text-2xs flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {duration}
          </Badge>
        </div>
      </div>
      
      {/* Content Section */}
      <div className="p-4 space-y-3">
        <div>
          <h3 className="font-display font-bold text-base text-foreground leading-tight line-clamp-2">
            {title}
          </h3>
          <p className="text-muted-foreground text-sm mt-1 leading-relaxed line-clamp-2">
            {description}
          </p>
        </div>
        
        {/* Stats */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Eye className="h-3 w-3" />
            <span>{views.toLocaleString()} tayangan</span>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onPlay}
            className="text-primary hover:text-primary/80 font-semibold p-0 h-auto"
          >
            Tonton Sekarang
          </Button>
        </div>
      </div>
      
      {/* Glow Effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-primary opacity-0 group-hover:opacity-5 transition-opacity duration-500 pointer-events-none" />
    </Card>
  );
}