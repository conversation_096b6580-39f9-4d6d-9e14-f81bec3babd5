import { useState, useEffect } from "react";
import { Header } from "@/components/header";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { CheckCircle, ArrowLeft, Home, Save } from "lucide-react";
import { useAssessmentSession } from "@/hooks/use-assessment-session";
import { useToast } from "@/hooks/use-toast";

const mscsQuestions = [
  "Ada seseorang yang istimewa di sekitar saya saat saya membutuhkannya",
  "Ada seseorang yang istimewa yang berbagi kegembiraan dan kesedihan saya",
  "<PERSON><PERSON>arga saya benar-benar mencoba membantu saya",
  "Saya mendapatkan dukungan dan bantuan emosional yang saya butuhkan dari keluarga saya",
  "Saya memiliki seseorang yang istimewa yang menjadi sumber penghiburan bagi saya",
  "Teman-teman saya benar-benar mencoba membantu saya",
  "Saya dapat mengandalkan teman-teman saya ketika hal-hal tidak berjalan dengan baik",
  "Saya dapat berbicara tentang masalah saya dengan keluarga saya",
  "Saya memiliki teman-teman yang berbagi kegembiraan dan kesedihan saya",
  "Ada seseorang yang istimewa dalam hidup saya yang peduli terhadap perasaan saya",
  "Keluarga saya bersedia membantu saya membuat keputusan",
  "Saya dapat berbicara tentang masalah saya dengan teman-teman saya"
];

const scaleLabels = [
  "Sangat Tidak Setuju",
  "Tidak Setuju",
  "Agak Tidak Setuju",
  "Netral",
  "Agak Setuju",
  "Setuju",
  "Sangat Setuju"
];

export default function MSCSAssessment() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const { session, createSession, saveAnswer, completeSession, isLoading } = useAssessmentSession('mscs');

  useEffect(() => {
    if (!session) {
      createSession();
    }
  }, []);

  const progress = ((currentQuestion + (selectedAnswer !== null ? 1 : 0)) / mscsQuestions.length) * 100;

  const handleAnswer = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = async () => {
    if (selectedAnswer === null || !session) return;

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = selectedAnswer;
    setAnswers(newAnswers);

    // Save answer to database
    await saveAnswer(`mscs_q${currentQuestion + 1}`, selectedAnswer);
    
    setSelectedAnswer(null);

    if (currentQuestion < mscsQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const result = calculateResult(newAnswers);
      await completeSession(result.totalScore, {
        familySupport: result.familyScore,
        friendsSupport: result.friendsScore,
        specialPersonSupport: result.specialPersonScore,
        averageScore: result.averageScore
      });
      setIsCompleted(true);
    }
  };

  const handleSaveProgress = async () => {
    if (selectedAnswer !== null && session) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      await saveAnswer(`mscs_q${currentQuestion + 1}`, selectedAnswer);
      
      toast({
        title: "Progress Tersimpan",
        description: "Jawaban Anda telah disimpan.",
      });
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(answers[currentQuestion - 1] ?? null);
    }
  };

  const calculateResult = (answersArray = answers) => {
    // MSCS subscales:
    // Special Person: items 0, 1, 4, 9 (questions 1, 2, 5, 10)
    // Family: items 2, 3, 7, 10 (questions 3, 4, 8, 11)
    // Friends: items 5, 6, 8, 11 (questions 6, 7, 9, 12)
    
    const specialPersonItems = [0, 1, 4, 9];
    const familyItems = [2, 3, 7, 10];
    const friendsItems = [5, 6, 8, 11];
    
    const specialPersonScore = specialPersonItems.reduce((sum, index) => sum + (answersArray[index] + 1), 0);
    const familyScore = familyItems.reduce((sum, index) => sum + (answersArray[index] + 1), 0);
    const friendsScore = friendsItems.reduce((sum, index) => sum + (answersArray[index] + 1), 0);
    
    const totalScore = specialPersonScore + familyScore + friendsScore;
    const averageScore = Math.round((totalScore / mscsQuestions.length) * 10) / 10;
    
    let category = "";
    if (averageScore >= 5.5) category = "Dukungan Tinggi";
    else if (averageScore >= 4) category = "Dukungan Sedang";
    else category = "Dukungan Rendah";

    return { 
      totalScore, 
      specialPersonScore, 
      familyScore, 
      friendsScore, 
      averageScore, 
      category 
    };
  };

  if (isCompleted) {
    const result = calculateResult();
    
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header 
          title="Hasil Assessment" 
          subtitle="Multidimensional Scale of Perceived Social Support"
        />
        
        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-gradient-primary w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            
            <h2 className="font-display font-bold text-2xl text-foreground mb-2">
              Assessment Selesai!
            </h2>
            <p className="text-muted-foreground mb-6">
              Terima kasih telah menyelesaikan MSCS Assessment
            </p>

            <div className="space-y-4">
              <div className="p-4 rounded-lg bg-primary-soft">
                <div className="text-3xl font-bold text-primary mb-1">
                  {result.averageScore}/7
                </div>
                <div className="text-sm text-muted-foreground">Skor Rata-rata</div>
              </div>

              <div className="grid grid-cols-3 gap-2">
                <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/20">
                  <div className="text-sm font-bold text-green-700 dark:text-green-300">
                    {Math.round((result.specialPersonScore / 4) * 10) / 10}
                  </div>
                  <div className="text-xs text-muted-foreground">Orang Istimewa</div>
                </div>
                
                <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20">
                  <div className="text-sm font-bold text-blue-700 dark:text-blue-300">
                    {Math.round((result.familyScore / 4) * 10) / 10}
                  </div>
                  <div className="text-xs text-muted-foreground">Keluarga</div>
                </div>
                
                <div className="p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20">
                  <div className="text-sm font-bold text-purple-700 dark:text-purple-300">
                    {Math.round((result.friendsScore / 4) * 10) / 10}
                  </div>
                  <div className="text-xs text-muted-foreground">Teman</div>
                </div>
              </div>

              <div className="p-4 rounded-lg bg-secondary">
                <div className="text-lg font-semibold text-foreground mb-1">
                  {result.category}
                </div>
                <div className="text-sm text-muted-foreground">
                  {result.category === "Dukungan Tinggi" && "Anda memiliki dukungan sosial yang sangat baik"}
                  {result.category === "Dukungan Sedang" && "Anda memiliki dukungan sosial yang cukup"}
                  {result.category === "Dukungan Rendah" && "Perlu memperkuat jaringan dukungan sosial Anda"}
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex-1"
              >
                Kembali ke Beranda
              </Button>
              <Button 
                onClick={() => navigate('/assessment')}
                className="flex-1 bg-gradient-primary"
              >
                Assessment Lain
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-soft">
      <Header 
        title="MSCS Assessment" 
        subtitle={`Pertanyaan ${currentQuestion + 1} dari ${mscsQuestions.length}`}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <h2 className="font-display font-semibold text-lg text-foreground mb-6 leading-relaxed">
            {mscsQuestions[currentQuestion]}
          </h2>

          <div className="space-y-3">
            {scaleLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(index)}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedAnswer === index
                    ? 'border-primary bg-primary-soft shadow-soft'
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index 
                      ? 'border-primary bg-primary' 
                      : 'border-muted-foreground'
                  }`}>
                    {selectedAnswer === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="font-medium text-foreground">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Navigation */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Sebelumnya
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 bg-gradient-primary"
            >
              {currentQuestion === mscsQuestions.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>

          {/* Progress controls */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Beranda
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Simpan Progress
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}