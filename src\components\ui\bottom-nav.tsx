import { cn } from "@/lib/utils";
import { Home, FileText, User, BookOpen, History, Menu } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useState } from "react";

const navItems = [
  { icon: Home, label: "Beranda", path: "/" },
  { icon: FileText, label: "Assessment", path: "/assessment" },
  { icon: BookOpen, label: "Eduka<PERSON>", path: "/education" },
  { icon: User, label: "Profil", path: "/profile" },
];

export function BottomNav() {
  const location = useLocation();
  const [activeItem, setActiveItem] = useState(location.pathname);

  return (
    <>
      {/* Modern Bottom Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 z-50 safe-bottom">
        {/* Glassmorphism Background */}
        <div className="glass border-t border-border-accent backdrop-blur-xl">
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-card-glass to-transparent pointer-events-none" />
          
          {/* Navigation Container */}
          <div className="relative flex items-center justify-around py-2 px-4 max-w-md mx-auto">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "touch-target flex flex-col items-center justify-center relative",
                    "transition-all duration-300 group focus-ring rounded-xl",
                    "hover:scale-105 active:scale-95"
                  )}
                  onClick={() => setActiveItem(item.path)}
                  aria-label={item.label}
                >
                  {/* Active Background Indicator */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-primary rounded-xl opacity-10 animate-scale-in" />
                  )}
                  
                  {/* Icon with Enhanced Styling */}
                  <div className={cn(
                    "relative p-2 rounded-lg transition-all duration-300",
                    isActive 
                      ? "text-primary scale-110" 
                      : "text-muted-foreground group-hover:text-foreground group-hover:scale-110"
                  )}>
                    <Icon className="h-5 w-5" />
                    
                    {/* Active Dot Indicator */}
                    {isActive && (
                      <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full animate-scale-in" />
                    )}
                  </div>
                  
                  {/* Label */}
                  <span className={cn(
                    "text-2xs font-medium transition-all duration-300 mt-1",
                    isActive 
                      ? "text-primary font-semibold" 
                      : "text-muted-foreground group-hover:text-foreground"
                  )}>
                    {item.label}
                  </span>
                  
                  {/* Hover Effect */}
                  <div className="absolute inset-0 rounded-xl bg-primary/5 scale-0 group-hover:scale-100 transition-transform duration-300" />
                </Link>
              );
            })}
          </div>
        </div>
        
        {/* Safe Area for iOS */}
        <div className="h-safe-bottom bg-card-glass" />
      </nav>
      
      {/* Spacer to prevent content overlap */}
      <div className="h-20 sm:h-16" />
    </>
  );
}