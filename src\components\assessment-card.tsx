import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Users, Brain, Heart, Shield, ArrowRight, CheckCircle2, PlayCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface AssessmentCardProps {
  title: string;
  description: string;
  duration: string;
  questions: number;
  category: "stress" | "confidence" | "knowledge" | "support" | "stigma";
  onStart: () => void;
  className?: string;
  style?: React.CSSProperties;
  completionRate?: number;
  lastCompleted?: string;
  difficulty?: "easy" | "medium" | "hard";
}

const categoryConfig = {
  stress: { 
    icon: Brain, 
    color: "bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/50 dark:to-orange-950/50", 
    badge: "Stress & Depresi",
    accent: "text-red-600 dark:text-red-400",
    gradient: "from-red-500 to-orange-500"
  },
  confidence: { 
    icon: Heart, 
    color: "bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/50 dark:to-cyan-950/50", 
    badge: "Kepercayaan Diri",
    accent: "text-blue-600 dark:text-blue-400",
    gradient: "from-blue-500 to-cyan-500"
  },
  knowledge: { 
    icon: Brain, 
    color: "bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50", 
    badge: "Pengetahuan",
    accent: "text-green-600 dark:text-green-400",
    gradient: "from-green-500 to-emerald-500"
  },
  support: { 
    icon: Users, 
    color: "bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/50 dark:to-pink-950/50", 
    badge: "Dukungan Sosial",
    accent: "text-purple-600 dark:text-purple-400",
    gradient: "from-purple-500 to-pink-500"
  },
  stigma: { 
    icon: Shield, 
    color: "bg-gradient-to-br from-amber-50 to-yellow-50 dark:from-amber-950/50 dark:to-yellow-950/50", 
    badge: "Stigma",
    accent: "text-amber-600 dark:text-amber-400",
    gradient: "from-amber-500 to-yellow-500"
  }
};

const difficultyConfig = {
  easy: { label: "Mudah", color: "bg-success-soft text-success-foreground" },
  medium: { label: "Sedang", color: "bg-warning-soft text-warning-foreground" },
  hard: { label: "Sulit", color: "bg-destructive-soft text-destructive-foreground" }
};

export function AssessmentCard({ 
  title, 
  description, 
  duration, 
  questions, 
  category, 
  onStart,
  className,
  style,
  completionRate = 0,
  lastCompleted,
  difficulty = "medium"
}: AssessmentCardProps) {
  const config = categoryConfig[category];
  const diffConfig = difficultyConfig[difficulty];
  const Icon = config.icon;
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card 
      className={cn(
        "group relative overflow-hidden border-0 shadow-soft hover:shadow-interactive",
        "transition-all duration-500 animate-fade-in card-interactive",
        "hover:scale-[1.02] focus-within:scale-[1.02]",
        config.color,
        className
      )}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className={cn("absolute inset-0 bg-gradient-to-br", config.gradient)} />
      </div>
      
      {/* Floating Elements */}
      <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-500">
        <div className={cn("w-20 h-20 rounded-full bg-gradient-to-br", config.gradient, "animate-float")} />
      </div>

      <div className="relative p-6 space-y-4">
        {/* Header Section */}
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            {/* Enhanced Icon */}
            <div className={cn(
              "p-3 rounded-2xl shadow-medium transition-all duration-300",
              "bg-white/90 dark:bg-black/20 border border-white/50",
              "group-hover:scale-110 group-hover:shadow-glow"
            )}>
              <Icon className={cn("h-6 w-6 transition-colors duration-300", config.accent)} />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <Badge 
                  variant="secondary" 
                  className="text-2xs font-semibold px-2 py-1"
                >
                  {config.badge}
                </Badge>
                <Badge 
                  className={cn("text-2xs px-2 py-1", diffConfig.color)}
                >
                  {diffConfig.label}
                </Badge>
              </div>
              
              <h3 className="font-display font-bold text-lg text-foreground leading-tight">
                {title}
              </h3>
            </div>
          </div>

          {/* Completion Status */}
          {completionRate > 0 && (
            <div className="flex items-center gap-1 text-success animate-scale-in">
              <CheckCircle2 className="h-4 w-4" />
              <span className="text-xs font-semibold">{completionRate}%</span>
            </div>
          )}
        </div>

        {/* Description */}
        <p className="text-muted-foreground text-sm leading-relaxed">
          {description}
        </p>

        {/* Progress Indicator */}
        {completionRate > 0 && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-semibold text-foreground">{completionRate}%</span>
            </div>
            <Progress value={completionRate} className="h-2" />
          </div>
        )}

        {/* Meta Information */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span className="font-medium">{duration}</span>
            </div>
            <div className="flex items-center gap-1">
              <PlayCircle className="h-3 w-3" />
              <span className="font-medium">{questions} pertanyaan</span>
            </div>
          </div>
          
          {lastCompleted && (
            <span className="text-2xs text-muted-foreground">
              Terakhir: {lastCompleted}
            </span>
          )}
        </div>

        {/* Action Button */}
        <Button 
          onClick={onStart}
          className={cn(
            "w-full h-12 font-semibold text-sm transition-all duration-300",
            "bg-gradient-primary hover:opacity-90 hover:scale-[1.02]",
            "shadow-interactive hover:shadow-glow focus:shadow-glow",
            "focus-ring group"
          )}
          aria-label={`Start ${title} assessment`}
        >
          <span className="flex items-center justify-center gap-2">
            {completionRate > 0 ? "Lanjutkan Assessment" : "Mulai Assessment"}
            <ArrowRight className={cn(
              "h-4 w-4 transition-transform duration-300",
              "group-hover:translate-x-1"
            )} />
          </span>
        </Button>
      </div>

      {/* Hover Glow Effect */}
      <div className={cn(
        "absolute inset-0 rounded-xl transition-opacity duration-500 pointer-events-none",
        "bg-gradient-to-br opacity-0 group-hover:opacity-20",
        config.gradient
      )} />
    </Card>
  );
}