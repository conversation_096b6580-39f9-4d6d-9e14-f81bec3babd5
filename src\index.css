@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System - Enhanced colors, gradients, shadows and effects */

@layer base {
  :root {
    /* Enhanced Background System */
    --background: 180 25% 97%;
    --foreground: 180 20% 12%;
    --surface: 0 0% 100%;
    --surface-variant: 180 15% 96%;

    /* Modern Card System */
    --card: 0 0% 100%;
    --card-foreground: 180 20% 12%;
    --card-elevated: 0 0% 100%;
    --card-glass: 0 0% 100% / 0.8;

    /* Logo-Inspired Green Primary Palette */
    --primary: 120 65% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-soft: 120 50% 88%;
    --primary-accent: 120 60% 55%;
    --primary-muted: 120 30% 75%;

    /* Logo-Inspired Cream Secondary System */
    --secondary: 45 35% 80%;
    --secondary-foreground: 120 25% 20%;
    --secondary-accent: 45 45% 70%;

    /* Enhanced Neutral System */
    --muted: 180 20% 94%;
    --muted-foreground: 180 15% 45%;
    --muted-accent: 180 25% 88%;

    /* Modern Accent Colors */
    --accent: 160 55% 62%;
    --accent-foreground: 180 20% 12%;
    --accent-soft: 160 40% 85%;

    /* Status Colors */
    --success: 145 70% 48%;
    --success-foreground: 0 0% 100%;
    --success-soft: 145 45% 85%;

    --warning: 45 95% 55%;
    --warning-foreground: 180 20% 12%;
    --warning-soft: 45 70% 88%;

    --destructive: 0 90% 58%;
    --destructive-foreground: 0 0% 100%;
    --destructive-soft: 0 60% 90%;

    /* Enhanced Borders & Inputs */
    --border: 180 20% 85%;
    --border-accent: 180 25% 80%;
    --input: 180 25% 90%;
    --ring: 175 70% 42%;

    /* Logo-Inspired Green Gradient System */
    --gradient-primary: linear-gradient(135deg, hsl(120 65% 45%) 0%, hsl(130 60% 48%) 50%, hsl(110 65% 50%) 100%);
    --gradient-secondary: linear-gradient(120deg, hsl(45 40% 85%) 0%, hsl(50 35% 80%) 100%);
    --gradient-accent: linear-gradient(45deg, hsl(120 55% 55%) 0%, hsl(130 60% 60%) 100%);
    --gradient-soft: linear-gradient(180deg, hsl(45 25% 97%) 0%, hsl(45 20% 94%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%) 0%, hsl(45 15% 97%) 100%);
    --gradient-glass: linear-gradient(135deg, hsl(0 0% 100% / 0.9) 0%, hsl(45 20% 98% / 0.7) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(120 65% 45%) 0%, hsl(130 60% 48%) 30%, hsl(110 65% 50%) 70%, hsl(120 55% 52%) 100%);

    /* Green-Themed Shadow System */
    --shadow-soft: 0 2px 25px hsl(120 65% 45% / 0.06);
    --shadow-medium: 0 8px 35px hsl(120 65% 45% / 0.12);
    --shadow-strong: 0 16px 45px hsl(120 65% 45% / 0.18);
    --shadow-glass: 0 8px 32px hsl(0 0% 0% / 0.08);
    --shadow-elevated: 0 20px 60px hsl(120 65% 45% / 0.15);
    --shadow-interactive: 0 4px 20px hsl(120 65% 45% / 0.15);

    /* Glassmorphism Effects */
    --glass-blur: blur(16px);
    --glass-border: 1px solid hsl(0 0% 100% / 0.2);
    --glass-bg: hsl(0 0% 100% / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Enhanced Dark Background System */
    --background: 180 20% 6%;
    --foreground: 180 15% 94%;
    --surface: 180 18% 8%;
    --surface-variant: 180 16% 10%;

    /* Dark Card System */
    --card: 180 18% 8%;
    --card-foreground: 180 15% 94%;
    --card-elevated: 180 16% 10%;
    --card-glass: 180 18% 8% / 0.8;

    /* Dark Primary Palette */
    --primary: 175 65% 58%;
    --primary-foreground: 180 20% 6%;
    --primary-soft: 175 45% 25%;
    --primary-accent: 175 55% 65%;
    --primary-muted: 175 35% 35%;

    /* Dark Secondary System */
    --secondary: 180 18% 15%;
    --secondary-foreground: 180 15% 85%;
    --secondary-accent: 140 40% 35%;

    /* Dark Neutral System */
    --muted: 180 18% 12%;
    --muted-foreground: 180 12% 65%;
    --muted-accent: 180 20% 18%;

    /* Dark Accent Colors */
    --accent: 160 50% 45%;
    --accent-foreground: 180 15% 94%;
    --accent-soft: 160 35% 25%;

    /* Dark Status Colors */
    --success: 145 65% 52%;
    --success-foreground: 180 20% 6%;
    --success-soft: 145 40% 20%;

    --warning: 45 90% 58%;
    --warning-foreground: 180 20% 6%;
    --warning-soft: 45 60% 25%;

    --destructive: 0 85% 62%;
    --destructive-foreground: 180 15% 94%;
    --destructive-soft: 0 55% 25%;

    /* Dark Borders & Inputs */
    --border: 180 18% 18%;
    --border-accent: 180 20% 22%;
    --input: 180 18% 15%;
    --ring: 175 65% 58%;

    /* Dark Gradient System */
    --gradient-primary: linear-gradient(135deg, hsl(175 65% 58%) 0%, hsl(160 60% 52%) 50%, hsl(145 65% 52%) 100%);
    --gradient-secondary: linear-gradient(120deg, hsl(175 45% 25%) 0%, hsl(160 40% 28%) 100%);
    --gradient-accent: linear-gradient(45deg, hsl(160 50% 45%) 0%, hsl(175 55% 48%) 100%);
    --gradient-soft: linear-gradient(180deg, hsl(180 20% 6%) 0%, hsl(180 18% 10%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(180 18% 8%) 0%, hsl(180 16% 12%) 100%);
    --gradient-glass: linear-gradient(135deg, hsl(180 18% 8% / 0.9) 0%, hsl(180 16% 10% / 0.7) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(175 65% 58%) 0%, hsl(160 60% 52%) 30%, hsl(145 65% 52%) 70%, hsl(175 60% 55%) 100%);

    /* Dark Enhanced Shadow System */
    --shadow-soft: 0 2px 25px hsl(0 0% 0% / 0.4);
    --shadow-medium: 0 8px 35px hsl(0 0% 0% / 0.5);
    --shadow-strong: 0 16px 45px hsl(0 0% 0% / 0.6);
    --shadow-glass: 0 8px 32px hsl(0 0% 0% / 0.3);
    --shadow-elevated: 0 20px 60px hsl(0 0% 0% / 0.4);
    --shadow-interactive: 0 4px 20px hsl(175 65% 58% / 0.2);

    /* Dark Glassmorphism Effects */
    --glass-blur: blur(20px);
    --glass-border: 1px solid hsl(180 15% 25% / 0.3);
    --glass-bg: hsl(180 18% 8% / 0.2);

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    scroll-behavior: smooth;
  }

  /* Enhanced Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* Focus Styles for Accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Enhanced Selection Styling */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Modern Glassmorphism Component */
  .glass {
    background: var(--gradient-glass);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    @apply shadow-glass;
  }

  /* Enhanced Card Variants */
  .card-elevated {
    @apply bg-card-elevated shadow-elevated;
  }

  .card-glass {
    @apply glass border-0;
  }

  .card-interactive {
    @apply transition-all duration-300 hover:shadow-interactive hover:-translate-y-1;
  }

  /* Modern Button Enhancements */
  .btn-glass {
    @apply glass hover:bg-primary/10 active:scale-95;
  }

  .btn-gradient {
    background: var(--gradient-primary);
    @apply text-primary-foreground shadow-interactive hover:shadow-strong transition-all duration-300 hover:scale-105 active:scale-95;
  }

  /* Touch Target Optimization */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Enhanced Focus States */
  .focus-ring {
    @apply focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background focus-visible:outline-none;
  }

  /* Modern Loading States */
  .skeleton-shimmer {
    background: linear-gradient(90deg, 
      hsl(var(--muted)) 0%, 
      hsl(var(--muted-accent)) 50%, 
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  /* Micro-interaction Classes */
  .micro-bounce {
    @apply transition-transform duration-200 hover:scale-105 active:scale-95;
  }

  .micro-slide {
    @apply transition-transform duration-300 hover:translate-x-1;
  }

  .micro-glow {
    @apply transition-shadow duration-300 hover:shadow-interactive;
  }

  /* Progressive Enhancement */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}