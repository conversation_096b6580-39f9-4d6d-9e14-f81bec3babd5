import { useState, useEffect } from "react";
import { Header } from "@/components/header";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useNavigate } from "react-router-dom";
import { CheckCircle, ArrowLeft, Home, Save } from "lucide-react";
import { useAssessmentSession } from "@/hooks/use-assessment-session";
import { useToast } from "@/hooks/use-toast";

const dass42Questions = [
  "Saya merasa sulit untuk tenang",
  "Saya merasa mulut saya kering",
  "Saya tidak dapat merasakan perasaan positif sama sekali",
  "Saya mengalami kesulitan bernapas (misalnya, napas cepat, sesak napas tanpa aktivitas fisik)",
  "Saya merasa sulit untuk memulai melakukan sesuatu",
  "Saya cenderung bereaksi berlebihan terhadap situasi",
  "Saya mengalami gemetar (misalnya, di tangan)",
  "Saya merasa menggunakan banyak energi mental",
  "Saya khawatir tentang situasi di mana saya mungkin panik dan mempermalukan diri sendiri",
  "Saya merasa tidak ada yang dapat dinanti-nantikan",
  "Saya merasa gelisah",
  "Saya merasa sulit untuk rileks",
  "Saya merasa sedih dan tertekan",
  "Saya tidak toleran terhadap hal-hal yang menghalangi saya untuk melanjutkan apa yang sedang saya lakukan",
  "Saya merasa hampir panik",
  "Saya tidak dapat merasa antusias tentang apa pun",
  "Saya merasa tidak berharga sebagai seseorang",
  "Saya merasa agak sensitif",
  "Saya menyadari kerja jantung saya tanpa aktivitas fisik (misalnya, perasaan detak jantung meningkat atau melewatkan detak)",
  "Saya merasa takut tanpa alasan yang jelas",
  "Saya merasa hidup tidak berarti",
  "Saya merasa gelisah",
  "Saya merasa sulit untuk menenangkan diri setelah sesuatu yang mengganggu saya",
  "Saya mengalami kesulitan menelan",
  "Saya tidak dapat menikmati hal-hal yang saya lakukan",
  "Saya menyadari aktivitas jantung saya saat istirahat (misalnya, detak jantung meningkat atau tidak teratur)",
  "Saya merasa putus asa",
  "Saya merasa gelisah dan tidak sabar",
  "Saya khawatir tentang situasi di mana saya mungkin panik dan mempermalukan diri sendiri",
  "Saya merasa gemetar (misalnya, di kaki)",
  "Saya merasa sulit untuk bekerja dengan inisiatif",
  "Saya cenderung bereaksi berlebihan terhadap situasi",
  "Saya merasa ngeri",
  "Saya merasa tidak ada yang dapat dinanti-nantikan",
  "Saya merasa sangat takut",
  "Saya dapat melihat bahwa hidup tidak berarti",
  "Saya merasa gelisah",
  "Saya khawatir tentang situasi di mana saya mungkin panik dan mempermalukan diri sendiri",
  "Saya mengalami gemetar (misalnya, di tangan)",
  "Saya merasa sulit untuk membuat keputusan",
  "Saya takut saya akan 'terlempar' oleh tugas-tugas sepele yang tidak biasa",
  "Saya tidak dapat merasa antusias tentang apa pun"
];

const scaleLabels = [
  "Tidak pernah",
  "Kadang-kadang", 
  "Sering",
  "Hampir selalu"
];

export default function DASS42Assessment() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<number[]>([]);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const { session, progress: loadedProgress, createSession, saveAnswer, completeSession, isLoading, loadLatestSession } = useAssessmentSession('dass42');

  useEffect(() => {
    const initialize = async () => {
      // Try to load the latest session, if it doesn't exist, create a new one.
      const existingSession = await loadLatestSession();
      if (!existingSession) {
        await createSession();
      }
    };
    initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // When progress is loaded from the hook, update the local state
    if (session && loadedProgress && Object.keys(loadedProgress).length > 0) {
        const loadedAnswers: number[] = [];
        let lastQuestionAnswered = -1;

        // Reconstruct the answers array from the progress object
        for (let i = 0; i < dass42Questions.length; i++) {
            const questionKey = `dass42_q${i + 1}`;
            if (loadedProgress[questionKey] !== undefined) {
                const answerValue = parseInt(loadedProgress[questionKey], 10);
                if (!isNaN(answerValue)) {
                  loadedAnswers[i] = answerValue;
                  lastQuestionAnswered = i;
                }
            }
        }
        setAnswers(loadedAnswers);

        // Set the current question to the one after the last answered question
        const nextQuestion = lastQuestionAnswered < dass42Questions.length - 1
          ? lastQuestionAnswered + 1
          : dass42Questions.length - 1;

        setCurrentQuestion(nextQuestion);
        setSelectedAnswer(loadedAnswers[nextQuestion] ?? null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadedProgress, session]);

  const progress = ((currentQuestion + (selectedAnswer !== null ? 1 : 0)) / dass42Questions.length) * 100;

  const handleAnswer = (value: number) => {
    setSelectedAnswer(value);
  };

  const handleNext = async () => {
    if (selectedAnswer === null || !session) return;

    const newAnswers = [...answers];
    newAnswers[currentQuestion] = selectedAnswer;
    setAnswers(newAnswers);

    // Save answer to database
    await saveAnswer(`dass42_q${currentQuestion + 1}`, selectedAnswer);
    
    setSelectedAnswer(null);

    if (currentQuestion < dass42Questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      const result = calculateResult(newAnswers);
      await completeSession(result.totalScore, {
        depression: result.depressionScore,
        anxiety: result.anxietyScore,
        stress: result.stressScore
      });
      setIsCompleted(true);
    }
  };

  const handleSaveProgress = async () => {
    if (selectedAnswer !== null && session) {
      const newAnswers = [...answers];
      newAnswers[currentQuestion] = selectedAnswer;
      setAnswers(newAnswers);
      await saveAnswer(`dass42_q${currentQuestion + 1}`, selectedAnswer);
      
      toast({
        title: "Progress Tersimpan",
        description: "Jawaban Anda telah disimpan.",
      });
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
      setSelectedAnswer(answers[currentQuestion - 1] || null);
    }
  };

  const calculateResult = (answersArray = answers) => {
    // DASS-42 scoring: Depression (items 3,5,10,13,16,17,21,24,26,31,34,37,38,42), 
    // Anxiety (items 2,4,7,9,15,19,20,23,25,28,30,36,40,41), 
    // Stress (items 1,6,8,11,12,14,18,22,27,29,32,33,35,39)
    
    const depressionItems = [2,4,9,12,15,16,20,23,25,30,33,36,37,41]; // 0-indexed
    const anxietyItems = [1,3,6,8,14,18,19,22,24,27,29,35,39,40]; // 0-indexed
    const stressItems = [0,5,7,10,11,13,17,21,26,28,31,32,34,38]; // 0-indexed
    
    const depressionScore = depressionItems.reduce((sum, index) => sum + (answersArray[index] || 0), 0) * 2;
    const anxietyScore = anxietyItems.reduce((sum, index) => sum + (answersArray[index] || 0), 0) * 2;
    const stressScore = stressItems.reduce((sum, index) => sum + (answersArray[index] || 0), 0) * 2;
    
    const totalScore = depressionScore + anxietyScore + stressScore;

    const getCategory = (score: number, type: string) => {
      if (type === 'depression') {
        if (score <= 9) return 'Normal';
        if (score <= 13) return 'Ringan';
        if (score <= 20) return 'Sedang';
        if (score <= 27) return 'Berat';
        return 'Sangat Berat';
      } else if (type === 'anxiety') {
        if (score <= 7) return 'Normal';
        if (score <= 9) return 'Ringan';
        if (score <= 14) return 'Sedang';
        if (score <= 19) return 'Berat';
        return 'Sangat Berat';
      } else { // stress
        if (score <= 14) return 'Normal';
        if (score <= 18) return 'Ringan';
        if (score <= 25) return 'Sedang';
        if (score <= 33) return 'Berat';
        return 'Sangat Berat';
      }
    };

    return { 
      totalScore, 
      depressionScore,
      anxietyScore,
      stressScore,
      depressionCategory: getCategory(depressionScore, 'depression'),
      anxietyCategory: getCategory(anxietyScore, 'anxiety'),
      stressCategory: getCategory(stressScore, 'stress')
    };
  };

  if (isCompleted) {
    const result = calculateResult();
    
    return (
      <div className="min-h-screen bg-gradient-soft">
        <Header 
          title="Hasil Assessment" 
          subtitle="DASS-42"
        />
        
        <div className="p-4 max-w-md mx-auto space-y-6">
          <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in text-center">
            <div className="p-4 rounded-full bg-gradient-primary w-20 h-20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            
            <h2 className="font-display font-bold text-2xl text-foreground mb-2">
              Assessment Selesai!
            </h2>
            <p className="text-muted-foreground mb-6">
              Terima kasih telah menyelesaikan DASS-42 Assessment
            </p>

            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-3">
                <div className="p-3 rounded-lg bg-red-50 dark:bg-red-950/20">
                  <div className="text-lg font-bold text-red-700 dark:text-red-300">
                    {result.depressionScore}
                  </div>
                  <div className="text-xs text-muted-foreground">Depresi</div>
                  <div className="text-xs font-medium text-red-600 dark:text-red-400">
                    {result.depressionCategory}
                  </div>
                </div>
                
                <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/20">
                  <div className="text-lg font-bold text-yellow-700 dark:text-yellow-300">
                    {result.anxietyScore}
                  </div>
                  <div className="text-xs text-muted-foreground">Kecemasan</div>
                  <div className="text-xs font-medium text-yellow-600 dark:text-yellow-400">
                    {result.anxietyCategory}
                  </div>
                </div>
                
                <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20">
                  <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                    {result.stressScore}
                  </div>
                  <div className="text-xs text-muted-foreground">Stres</div>
                  <div className="text-xs font-medium text-blue-600 dark:text-blue-400">
                    {result.stressCategory}
                  </div>
                </div>
              </div>

              <div className="p-4 rounded-lg bg-secondary">
                <div className="text-lg font-semibold text-foreground mb-1">
                  Total Skor: {result.totalScore}/126
                </div>
                <div className="text-sm text-muted-foreground">
                  Hasil menunjukkan tingkat depresi, kecemasan, dan stres Anda
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button 
                variant="outline" 
                onClick={() => navigate('/')}
                className="flex-1"
              >
                Kembali ke Beranda
              </Button>
              <Button 
                onClick={() => navigate('/assessment')}
                className="flex-1 bg-gradient-primary"
              >
                Assessment Lain
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-soft">
      <Header 
        title="DASS-42 Assessment" 
        subtitle={`Pertanyaan ${currentQuestion + 1} dari ${dass42Questions.length}`}
      />
      
      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card className="p-6 shadow-medium border-0 bg-gradient-card animate-fade-in">
          <h2 className="font-display font-semibold text-lg text-foreground mb-6 leading-relaxed">
            {dass42Questions[currentQuestion]}
          </h2>

          <div className="space-y-3">
            {scaleLabels.map((label, index) => (
              <button
                key={index}
                onClick={() => handleAnswer(index)}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                  selectedAnswer === index
                    ? 'border-primary bg-primary-soft shadow-soft'
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedAnswer === index 
                      ? 'border-primary bg-primary' 
                      : 'border-muted-foreground'
                  }`}>
                    {selectedAnswer === index && (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="font-medium text-foreground">{label}</span>
                </div>
              </button>
            ))}
          </div>
        </Card>

        {/* Navigation */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Sebelumnya
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 bg-gradient-primary"
            >
              {currentQuestion === dass42Questions.length - 1 ? 'Selesai' : 'Selanjutnya'}
            </Button>
          </div>

          {/* Progress controls */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Beranda
            </Button>
            
            <Button
              variant="outline"
              onClick={handleSaveProgress}
              disabled={selectedAnswer === null || isLoading}
              className="flex-1 flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Simpan Progress
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}