import { Header } from "@/components/header";
import { AssessmentCard } from "@/components/assessment-card";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Home } from "lucide-react";

const assessments = [
  {
    id: "dass42",
    title: "DASS-42",
    description: "Mengukur tingkat depresi, kec<PERSON><PERSON>, dan stres melalui 42 pertanyaan komprehensif",
    duration: "10-15 menit",
    questions: 42,
    category: "stress" as const,
  },
  {
    id: "gse",
    title: "General Self-Efficacy Scale",
    description: "Menilai keyakinan diri dalam mengatasi berbagai situasi dan tantangan hidup",
    duration: "3-5 menit",
    questions: 10,
    category: "confidence" as const,
  },
  {
    id: "mhkq",
    title: "Mental Health Knowledge Questionnaire",
    description: "Mengevaluasi tingkat pengetahuan tentang kesehatan mental dan perawatannya",
    duration: "5-8 menit",
    questions: 15,
    category: "knowledge" as const,
  },
  {
    id: "mscs",
    title: "Multidimensional Scale of Perceived Social Support",
    description: "Mengukur persepsi dukungan sosial dari keluarga, teman, dan orang terdekat",
    duration: "4-6 menit",
    questions: 12,
    category: "support" as const,
  },
  {
    id: "pdd",
    title: "Perceived Devaluation-Discrimination Scale",
    description: "Menilai persepsi stigma dan diskriminasi terhadap masalah kesehatan mental",
    duration: "5-7 menit",
    questions: 12,
    category: "stigma" as const,
  },
];

export default function Assessment() {
  const navigate = useNavigate();

  const handleStartAssessment = (assessmentId: string) => {
    navigate(`/assessment/${assessmentId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-soft pb-20">
      <Header 
        title="Assessment" 
        subtitle="Pilih assessment kesehatan mental"
      />
      
      <div className="p-4 max-w-md mx-auto space-y-4">
        <div className="text-center py-6">
          <h2 className="font-display font-semibold text-lg text-foreground mb-2">
            Pilih Assessment
          </h2>
          <p className="text-muted-foreground text-sm leading-relaxed mb-4">
            Setiap assessment dirancang untuk mengukur aspek kesehatan mental yang berbeda. 
            Pilih yang sesuai dengan kebutuhan Anda.
          </p>
          <Button
            variant="outline"
            onClick={() => navigate('/')}
            className="flex items-center gap-2"
          >
            <Home className="h-4 w-4" />
            Kembali ke Beranda
          </Button>
        </div>

        {assessments.map((assessment, index) => (
          <AssessmentCard
            key={assessment.id}
            title={assessment.title}
            description={assessment.description}
            duration={assessment.duration}
            questions={assessment.questions}
            category={assessment.category}
            onStart={() => handleStartAssessment(assessment.id)}
            className="animate-slide-up"
            style={{ animationDelay: `${index * 100}ms` }}
          />
        ))}
      </div>
      
      <BottomNav />
    </div>
  );
}