import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Assessment from "./pages/Assessment";
import Profile from "./pages/Profile";
import GSEAssessment from "./pages/GSEAssessment";
import DASS42Assessment from "./pages/DASS42Assessment";
import MHKQAssessment from "./pages/MHKQAssessment";
import PDDAssessment from "./pages/PDDAssessment";
import MSCSAssessment from "./pages/MSCSAssessment";
import History from "./pages/History";
import Education from "./pages/Education";
import VideoPlaylist from "./pages/VideoPlaylist";
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import ChangePassword from "./pages/ChangePassword";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/assessment" element={<Assessment />} />
          <Route path="/assessment/gse" element={<GSEAssessment />} />
          <Route path="/assessment/dass42" element={<DASS42Assessment />} />
          <Route path="/assessment/mhkq" element={<MHKQAssessment />} />
          <Route path="/assessment/pdd" element={<PDDAssessment />} />
          <Route path="/assessment/mscs" element={<MSCSAssessment />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/history" element={<History />} />
          <Route path="/education" element={<Education />} />
          <Route path="/videos" element={<VideoPlaylist />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/change-password" element={<ChangePassword />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
