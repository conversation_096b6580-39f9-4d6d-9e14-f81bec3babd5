import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
  updatePassword: (password: string) => Promise<{ error: any }>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
      } else {
        setSession(session);
        setUser(session?.user ?? null);
      }
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Handle auth events
        switch (event) {
          case 'SIGNED_IN':
            toast({
              title: "Login Berhasil",
              description: "Selamat datang kembali!",
            });
            break;
          case 'SIGNED_OUT':
            toast({
              title: "Logout Berhasil",
              description: "Anda telah keluar dari akun.",
            });
            // Clear any local storage data related to anonymous users
            localStorage.removeItem('assessment_user_id');
            break;
          case 'PASSWORD_RECOVERY':
            toast({
              title: "Reset Password",
              description: "Silakan ubah password Anda.",
            });
            break;
          case 'USER_UPDATED':
            toast({
              title: "Profil Diperbarui",
              description: "Informasi profil Anda telah diperbarui.",
            });
            break;
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [toast]);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Login Gagal",
          description: error.message,
          variant: "destructive",
        });
      }

      return { error };
    } catch (error) {
      const errorMessage = "Terjadi kesalahan saat login";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { error: { message: errorMessage } };
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        toast({
          title: "Registrasi Gagal",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Registrasi Berhasil",
          description: "Silakan cek email Anda untuk verifikasi akun",
        });
      }

      return { error };
    } catch (error) {
      const errorMessage = "Terjadi kesalahan saat registrasi";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { error: { message: errorMessage } };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast({
          title: "Logout Gagal",
          description: error.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Terjadi kesalahan saat logout",
        variant: "destructive",
      });
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/change-password`,
      });

      if (error) {
        toast({
          title: "Gagal Mengirim Email",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Email Terkirim",
          description: "Silakan cek email Anda untuk reset password",
        });
      }

      return { error };
    } catch (error) {
      const errorMessage = "Terjadi kesalahan saat mengirim email";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { error: { message: errorMessage } };
    }
  };

  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
      });

      if (error) {
        toast({
          title: "Gagal Mengubah Password",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Password Berhasil Diubah",
          description: "Password Anda telah berhasil diperbarui",
        });
      }

      return { error };
    } catch (error) {
      const errorMessage = "Terjadi kesalahan saat mengubah password";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return { error: { message: errorMessage } };
    }
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
